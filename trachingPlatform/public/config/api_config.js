/**
 * 公共地址配置
 */
// window.PUBLICPYTHON = 'https://p1.chinazdap.com' // python 在线运行请求地址
// window.PUBLICPYTHON2 = 'http://***************:7006' // python 在线运行请求地址
// window.CURRENT_HOST = "http://*************:39601" // 当前域名
// window.PUBLICHOSTA = 'http://**************:30031' // api请求地址 测试
// window.PUBLICHOSTA = window.__ENV__ && window.__ENV__.API_URL // 默认使用生产环境地址
window.PUBLICHOSTA = 'https://teach.fendoubi.com/api/' // api请求地址 测试
// window.PUBLICHOSTA = '/api/' // api请求地址 正式
window.IFRAME_PATH = "http://*************:39709" // 嵌入iframe的地址

window.LOOKFILEIP = `${window.PUBLICHOSTA}OSS/GetObjectUrl` // 查看上传文件api
window.FILEIP = `${window.PUBLICHOSTA}OSS/UploadSingleFile` // 文件上传api
window.REUPLOAD_FILE_URL = 'http://*************:9992/api/v1/UploadFiles/Reupload' // 文件重新（覆盖）上传api
const FileUploadUrlPrefix = 'http://*************:9992/api/v1/UploadFiles/';
window.FileChunkUpload = {
    InitUpload: FileUploadUrlPrefix + 'InitUpload', // 初始化上传
    UploadChunk: FileUploadUrlPrefix + 'UploadChunk', // 文件分片上传
    CompleteUpload: FileUploadUrlPrefix + 'CompleteUpload', // 完成上传
    AbortUpload: FileUploadUrlPrefix + 'AbortUpload', // 终止上传文件
};