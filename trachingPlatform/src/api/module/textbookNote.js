import request from "@/utils/request"

const textbookNoteApi = {
  // 获取笔记树结构列表
  GetNoteTree(params) {
    return request.send("/TextbookNote/GetList", params, "GET")
  },

  // 获取笔记详情
  GetNoteDetail(params) {
    return request.send("/TextbookNote/GetById", params, "GET")
  },

  // 新增笔记
  CreateNote(params) {
    return request.send("/TextbookNote/Create", params, "POST")
  },

  // 编辑笔记
  UpdateNote(params) {
    return request.send(`/TextbookNote/Update?id=${params.id}`, params, "POST")
  },

  // 删除笔记
  DeleteNote(params) {
    return request.send("/TextbookNote/Delete", params, "POST")
  },
}
export default textbookNoteApi
