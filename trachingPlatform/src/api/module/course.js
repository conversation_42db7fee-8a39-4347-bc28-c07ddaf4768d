import request from "@/utils/request"
/**
 * 课程管理
 */
const courseApi = {
  // 获取课程列表
  GetCourseList(params) {
    return request.send("/Course/GetCourseList", params, "GET")
  },
  // 获取课程详情
  GetCourseDetail(params) {
    return request.send("/Course/GetCourseDetail", params, "GET")
  },
  // 新增课程
  CreateCourse(params) {
    return request.send("/Course/CreateCourse", params, "POST")
  },
  // 编辑课程
  UpdateCourse(params) {
    return request.send(`/Course/UpdateCourse?id=${params.id}`, params, "POST")
  },
  // 删除课程
  DeleteCourse(params) {
    return request.send("/Course/DeleteCourse", params, "DELETE")
  },
  // 教师节目 我的课程
  MyCourses(params) {
    return request.send("/Course/MyCourses", params, "GET")
  },
  // 教师课程置顶
  SetMyCourseToTop(params) {
    return request.send("/Course/SetMyCourseToTop", params, "POST")
  },
  // 教师课程绑定班级
  BindTeachClassCourse(params) {
    return request.send("/Course/BindTeachClassCourse", params, "POST")
  },
  //

  // 教师课程资源创建
  TeacherCourseResourceCreate(params) {
    return request.send("/Course/TeacherCourseResourceCreate", params, "POST")
  },
  // 教师课程资源重命名
  TeacherCourseResourceRename(params) {
    return request.send("/Course/TeacherCourseResourceRename", params, "POST")
  },
  // 教师课程资源移动
  TeacherCourseResourceMove(params) {
    return request.send(`/Course/TeacherCourseResourceMove?targetFolderId=${params.targetFolderId}&fileId=${params.fileId}`, params, "POST")
  },
  // 教师课程资源删除
  TeacherCourseResourceDelete(params) {
    return request.send(`/Course/TeacherCourseResourceDelete?id=${params.id}`, params, "POST")
  },
  // 教师课程资源批量删除
  TeacherCourseResourceBatchDelete(params) {
    return request.send("/Course/TeacherCourseResourceBatchDelete", params, "POST")
  },
  // 教师课程资源树
  TeacherCourseResourceTree(params) {
    return request.send("/Course/TeacherCourseResourceTree", params, "GET")
  },
  // "我的"课程列表, 表示当前学生参与的课程
  MyCoursesOfStudent(params) {
    return request.send("/Course/MyCoursesOfStudent", params, "GET")
  },
  // 课程评价 - 学生添加/修改课程评论, 一个学生针对一个课程仅能添加一个评论
  StudentCourseCommentManage(params) {
    return request.send("/Course/StudentCourseCommentManage", params, "POST")
  },
  // 删除评价
  StudentCourseCommentDelete(params) {
    return request.send(`/Course/StudentCourseCommentDelete?id=${params.id}`, params, "POST")
  },
  // 课程评价 - 学生获取自己对课程的评价, 没有评论过则返回null
  StudentGetMyCourseComment(params) {
    return request.send("/Course/StudentGetMyCourseComment", params, "GET")
  },
  // 课程评价 - 获取某个课程的所有学生评价
  StudentCourseCommentList(params) {
    return request.send("/Course/StudentCourseCommentList", params, "GET")
  },
}
export default courseApi
