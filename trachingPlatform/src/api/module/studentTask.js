// 
import request from "@/utils/request";
/**
 * 任务管理
 */
const studentTaskApi = {
    // 获取题目列表
    GetStudentTaskQuestionList(params) {
        return request.send("/StudentTask/GetStudentTaskQuestionList", params, "POST");
    },
    // 学生提交作答
    SubmitAnswer(params) {
        return request.send("/StudentTask/SubmitAnswer", params, "POST");
    },
    // 学生提交考试
    SubmitExam(params) {
        return request.send("/StudentTask/SubmitExam", params, "POST");
    },
    // 获取学生任务列表
    GetStudentTaskList(params) {
        return request.send("/Task/GetStudentTaskList", params, "GET");
    },
    // 获取单个任务详情
    GetTaskById(params) {
        return request.send("/Task/GetById", params, "GET");
    },
    // 获取单个学生作答详情
    GetGradeDetail(params) {
        return request.send("/StudentTask/GetGradeDetail", params, "GET");
    },
};
export default studentTaskApi