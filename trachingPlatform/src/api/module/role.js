// 
import request from "@/utils/request";
/**
 * 角色管理
 */
const roleApi = {
    // 获取角色组列表
    RoleGetGroupList(params) {
        return request.send("/Role/GetGroupList", params, "GET");
    },
    // 创建角色组
    RoleCreateGroup(params) {
        return request.send("/Role/CreateGroup", params, "POST");
    },
    // 更新角色组
    RoleUpdateGroup(params) {
        return request.send(`/Role/UpdateGroup?id=${params.id}`, params, "POST");
    },
    // 删除角色组
    RoleDeleteGroup(params) {
        return request.send(`/Role/DeleteGroup`, params, "DELETE");
    },
    // 获取角色列表
    RoleGetRoleList(params) {
        return request.send("/Role/GetRoleList", params, "GET");
    },
    // 创建角色
    RoleCreateRole(params) {
        return request.send("/Role/CreateRole", params, "POST");
    },
    // 更新角色
    RoleUpdateRole(params) {
        return request.send(`/Role/UpdateRole?id=${params.id}`, params, "POST");
    },
    // 删除角色
    RoleDeleteRole(params) {
        return request.send(`/Role/DeleteRole?id=${params.id}`, params, "DELETE");
    },
    // 获取权限配置列表
    RoleGetPermissions(params) {
        return request.send("/Role/GetPermissions", params, "GET");
    },
    // 更新角色权限
    RoleUpdateRolePermission(params) {
        return request.send("/Role/UpdateRolePermission", params, "POST");
    },
};
export default roleApi