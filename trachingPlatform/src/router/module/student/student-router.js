export default [
  {
    path: "/student",
    name: "Student",
    component: () => import("@/views/studentPage/layout/index.vue"),
    meta: {
      title: "我的课程",
    },
    redirect: "/student/home",
    children: [
      {
        path: "/student/home",
        name: "StudentHome",
        component: () => import("@/views/studentPage/home/<USER>"),
        meta: {
          title: "我的课程",
        },
      },
      {
        path: "/student/course",
        name: "StudentCourse",
        component: () => import("@/views/studentPage/course/layout/index.vue"),
        redirect: "/student/course/resource",
        children: [
          {
            path: "/student/course/resource",
            name: "StudentCourseResource",
            component: () =>
              import("@/views/studentPage/course/modules/resource/index.vue"),
            meta: {
              title: "课程资源",
            },
          },
          {
            path: "/student/course/textbook",
            name: "StudentCourseTextbook",
            component: () =>
              import("@/views/studentPage/course/modules/textbook/index.vue"),
            meta: {
              title: "关联教材",
            },
          },
          {
            path: "/student/course/homework",
            name: "StudentCourseHomework",
            component: () =>
              import("@/views/studentPage/course/modules/homework/index.vue"),
            meta: {
              title: "课程作业",
            },
          },
          {
            path: "/student/course/exam",
            name: "StudentCourseExam",
            component: () =>
              import("@/views/studentPage/course/modules/exam/index.vue"),
            meta: {
              title: "课程考试",
            },
          },
          {
            path: "/student/course/note",
            name: "StudentCourseNote",
            component: () =>
              import("@/views/studentPage/course/modules/note/index.vue"),
            meta: {
              title: "课程笔记",
            },
          },
          {
            path: "/student/course/analysis",
            name: "StudentCourseAnalysis",
            component: () =>
              import("@/views/studentPage/course/modules/analysis/index.vue"),
            meta: {
              title: "学情分析",
            },
          },
          {
            path: "/student/course/info",
            name: "StudentCourseInfo",
            component: () =>
              import("@/views/studentPage/course/modules/info/index.vue"),
            meta: {
              title: "课程信息",
            },
          },
          {
            path: "/student/course/AI",
            name: "StudentCourseAI",
            component: () =>
              import("@/views/studentPage/course/modules/AI/index.vue"),
            meta: {
              title: "AI助手",
            },
          },
        ],
      },
      {
        path: "/student/task",
        name: "StudentTask",
        component: () => import("@/views/studentPage/task/index.vue"),
        meta: {
          title: "任务作答",
        },
      },
      {
        path: "/student/task/result",
        name: "StudentTaskResult",
        component: () => import("@/views/studentPage/task/result.vue"),
        meta: {
          title: "任务结果",
        },
      },
    ],
  },

  {
    path: "/student/editor/note",
    name: "StudentEditorNote",
    component: () => import("@/views/studentPage/editor/note/index.vue"),
    meta: {
      title: "笔记编辑",
    },
  }
]
