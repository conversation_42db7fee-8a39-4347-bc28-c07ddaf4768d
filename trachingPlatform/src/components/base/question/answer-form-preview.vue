<template>
  <div class="answer-form-preview">
    <div class="question-title">
      <div :class="[type == 50?'question-bold':'question-l']" v-if="showTitle">{{sort}}、{{ title }}</div>
    </div>
    <div :class="[type == 50?'question-descbg':'question-desc']" v-if="desc" style="" v-html="desc"></div>
    <template v-if="type === questionTypeMenu.radio">
      <option-preview v-model="newValue" :config="config" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>
    </template>
    <template v-else-if="type === questionTypeMenu.checkbox">
      <option-preview v-model="newValue" :config="config" multiple :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>
    </template>
    <template v-else-if="type === questionTypeMenu.content">
      <content-preview ref="previewCompsRef" v-model="newValue" :config="config" :disabled="disabled" :showAnswer="showAnswer"
      :error-info="errorInfo"></content-preview>
    </template>
    <template v-else-if="type === questionTypeMenu.shortAnswer">
      <short-preview ref="previewCompsRef" v-model="newValue" :config="config" :disabled="disabled" :showAnswer="showAnswer"
      :error-info="errorInfo"></short-preview>
    </template>
  </div>
</template> 

<script>
import { questionTypeMenu, questionTypeLabel } from './util.js'
import eventBus from "@/utils/eventBus";
export default {
  name: 'answer-form-preview',
  props: {
    type: { // 试题类型
      type: String | Number,
      default: ''
    },
    config: { // 试题对应配置项
      type: Object,
      default: () => {}
    },
    confighead: { // 试题对应配置项
      type: String,
      default:''
    },
    configdata: { // 试题对应配置项
      type: String,
      default:''
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    disabled: { // 是否禁用
      type: Boolean,
      default: false
    },
    answerValue: { // 答题值
      type: Array | Object | String | Number,
      default: false
    },
    showAnswer: { // 是否显示正确答案
      type: Boolean,
      default: false
    },
    sort: { // 题目序号
      type: Number,
      default: 0
    },
    showTitle: { // 是否显示title
      type: Boolean,
      default: true
    },
    readOnly: { // 是否只读
      type: Boolean,
      default: true
    },
    error: { // 错误答案信息
      type: String,
      default: ''
    },
    isDoTask: { // 是否是学生作答
      type: Boolean,
      default: false
    },
    isAnswerHint:{ // 是否是答案解析
      type: Boolean,
      default: false
    },
    isshowscore:{ // 是否展示分数
      type: Boolean,
      default: false
    },
    isshowAnalysis:{ // 是否展示答案解析
      type: Boolean,
      default: false
    },
    isshowDel:{ // 是否展示答案解析
      type: Boolean,
      default: false
    },
    isshowBtn:{ // 是否展示保存按钮
      type: Boolean,
      default: false
    },
    gradeId:{ // 作答记录gradeId
      type: Number,
      default: 0
    },
    publishTask:{ // 是否是任务发布 针对综合题
      type: Boolean,
      default: false
    }
  },
  components: {
    'option-preview': () => import('./options/preview.vue'),
    'content-preview': () => import('./content/preview.vue'),
    'short-preview': () => import('./short-answer/preview.vue'),
  },
  computed: {
    newValue: {
      get () {
        // value类型判断没做
        console.log(this.type,"答案记录---",this.type,this.answerValue);
        if(this.type == this.questionTypeMenu.content||this.type == this.questionTypeMenu.checkbox){
          return typeof this.answerValue == 'object'?(this.answerValue.longArray||this.answerValue||{}): this.answerValue?JSON.parse(this.answerValue).longArray:''
        } else if(this.type == this.questionTypeMenu.radio){
          let new_answer= null;
          if(typeof this.answerValue == 'object'){
            let answer = this.answerValue.longArray||this.answerValue||{}
            new_answer = answer.join(',')||''
          }else{
            let answer= ''
            try {
              answer = JSON.parse(this.answerValue).longArray||[]
              new_answer = answer.join(',')||''
            } catch (error) {
              answer = this.answerValue||''
              new_answer = answer
            }

          }
          return new_answer
        } else if(this.type == this.questionTypeMenu.shortAnswer){
          if(this.answerValue){
            return JSON.parse(this.answerValue).answer||''
          }
        }else {
          return this.answerValue
        }
      },
      set (val) {
        this.$emit('update:answerValue', val)
      }
    }
  },
  data () {
    return {
      questionTypeMenu: questionTypeMenu,
      questionTypeLabel: questionTypeLabel,
      errorInfo: [],
      isOther:true,
      confignoVal:''

    }
  },
  watch:{
    confighead:{
      handler(val){
       this.confignoVal=val
      },
      immediate:true
    },
    configdata:{
      handler(val){
         if(val){
          let JSONval = typeof val=='string'? JSON.parse(val):val;
          if(JSONval.data){
            let paramsno={
              data:{
                auditItem:JSONval?.data?.auditItem,
                bodyItems:JSONval?.data?.bodyItems
              }
            }
            let params={
              data:{
                headItem:JSONval?.data?.headItem,
                auditItem:JSONval?.data?.auditItem,
                bodyItems:JSONval?.data?.bodyItems
              }
            }
  
           let lastparams =this.confighead?paramsno:params
           this.answerValue=JSON.stringify(lastparams)
          }
        }
      },
      immediate:true
    },
    error:{
      handler(val){ // 错误对比
       this.handleSpreadCompare(val)
      },
      immediate:true,
      deep:true,
    },
    // config:{
    //   handler(val){ 
    //      console.log("val--config----------",this.val) 
    //   },
    // }
  },
  created () {
    // this.handleSpreadCompare(this.error)
    eventBus.$on("handlepreviewstatus", data => {
      this.isOther=true
    });
  },
  methods: {
    handleOpvocher(){
       this.isOther=!this.isOther
       this.$refs.previewCompsRef.handleOpvocher()
    },
    handleSpreadCompare (error) {
      if (error) {
        switch (this.type) {
          case questionTypeMenu.content:
          case questionTypeMenu.python:
          case questionTypeMenu.pythonCode:
            this.errorInfo = JSON.parse(error).map(item => item.path.split('.')[1])
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
          case questionTypeMenu.table:
            this.errorInfo = JSON.parse(error)
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
          default:
            this.errorInfo = JSON.parse(error).map(item => item.value)
        }
      } else {
        this.errorInfo = []
        switch (this.type) {
          case questionTypeMenu.content:
          case questionTypeMenu.python:
          case questionTypeMenu.table:
          case questionTypeMenu.pythonCode:
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.answer-form-preview {
  // display: grid;
  grid-gap: 15px;
  width: 100%;
  .question-title, .question-desc {
    width: 100%;
    word-break: break-all;
    line-height: 1.5;
  }
  .question-descbg{
      width: 100%;
      word-break: break-all;
      line-height: 1.5;
      background: #F9F9F9;
      border: 1px dashed #E0E0E0;
      padding: 15px;
      color: #333333;
      margin-bottom: 15px;
    }
  .question-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    .question-bold{
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
    }
    .writevocher{
        width: 96px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
      .writeback{
        width: 70px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
    }
  :deep(img) {
    max-width: 100%;
  }
}
</style>