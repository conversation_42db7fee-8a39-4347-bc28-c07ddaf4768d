<template>
  <div class="options-preview">
    <div v-for="option in optionsList" :key="option.value"
         :class="errorInfo && errorInfo.includes(option.value) ? 'error-options-item' : ''"
         class="options-list-item">
      <option-select-tag :isActive="isActive(option)"
                         :text="option.value"
                         @changeActive="changeActive(option)">
      </option-select-tag>
      <div class="" style="line-height: 20px;">
        <pre style="text-wrap: wrap;">{{ option.label }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import OptionSelectTag from '../option-tag/index.vue';

export default {
  name: 'options-preview',
  components: {
    OptionSelectTag
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    value: {
      type: String | Array,
      default: ''
    },
    showAnswer: { // 是否显示正确答案
      type: Boolean,
      default: false
    },
    errorInfo: { // 错误信息
      type: Array,
      default: () => []
    }
  },
  computed: {
    newValue: {
      get () {
        // value类型判断没做
        return this.value ? this.value : (this.multiple ? [] : '')
      },
      set (val) {
        this.$emit('input', val)
      }
    },
    optionsList: function () {
      return this.config.options || []
    }
  },
  methods: {
    isActive (option) {
      if (this.showAnswer) {
        return option.isAnswer
      }
      if (this.multiple) {
          return this.newValue?this.newValue.includes(option.value):false;
      } else {
        return option.value === this.newValue
      }
    },
    changeActive (option) {
      if (this.disabled) return

      if (this.multiple) {
        const currentValue = this.newValue || []
        const index = currentValue.indexOf(option.value)
        let newArray
        
        if (index === -1) {
          // 添加选项
          newArray = [...currentValue, option.value]
        } else {
          // 移除选项
          newArray = currentValue.filter(item => item !== option.value)
        }
        
        this.newValue = newArray
      } else {
        this.newValue = option.value
      }

      console.log("---",this.newValue)
      this.$emit('change', option)
    }
  }
}
</script>

<style scoped lang="scss">
.options-preview {
  .options-list-item {
    display: flex;
    margin-bottom: 10px;
    //align-items: center;
    align-items: flex-start;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .error-options-item {
    color: var(--color-danger);
    :deep(.option-select-tag-active) {
      background-color: var(--color-danger) !important;
      border-color: var(--color-danger) !important;
    }
  }
}
</style>