<template>
  <div v-if="type" class="question-preview-content">
    <div v-if="showForm&&showTiele" class="type-text">{{ questionTypeLabel[type] }}</div>
    <form-preview v-if="showForm"
                  :type="type"
                  :config="optionConfig"
                  :title="title"
                  :answerValue="answerValue"
                  :desc="desc"
                  :showAnswer="showAnswerInfo"
                  :disabled="type ==50 ? true : disabled"
                  :isAnswerHint="isAnswerHint"
                  :publishTask="publishTask"
                  :sort="sort"
                  :readOnly="readOnly"
                  :showTitle="showTitle"
                  style="border-bottom: 1px dashed #DBDBDB; padding-bottom: 20px;">
    </form-preview>
    <synthesisview 
     v-if="type==50 && isAnswer"
     :type="type"
     :config="optionConfig"
     :title="title"
     :answerValue="answerValue"
     :desc="desc"
     :showAnswer="false"
     >
     </synthesisview>

    <div class="hint-info" v-if="isAnswerHint">
      <div class="row" v-if="type==5 || type==50 ?false:true">
        <div class="row-label">答案</div>
        <el-input ref="answerInput" class="code-area" v-if="type==46&&hasClosedHtmlTags(answerText)" type="textarea" v-model="answerText" readonly></el-input>
        <div v-else :class="answerText ? '' : 'null-data'"  v-html="type == 49?'见上图':answerText" style="padding-bottom: 10px;"></div>
      </div>
      <div class="row" style="margin-top:20px;">
        <div class="row-label">答案解析</div>
        <div class="hint-content" style="line-height: 1.5;" :class="answerDetail ? '' : 'null-data'" v-html="answerDetail"></div>
      </div>
      <div class="row flex-box">
        <div class="row-label">难易度</div>
        <div :class="difficulty === '' ? 'null-data' : ''">{{ difficultyText }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {questionTypeLabel, getAnswerDispatch, getDifficultyDispatch,hasClosedHtmlTags} from '@/components/base/question/util.js';
import cloneDeep from "lodash/cloneDeep";

export default {
  name: 'question-preview-content',
  props: {
    isPreviewoperation: {
      type: Number,
      default:0
    },
    data: {
      type: Object,
      default: () => null
    },
    showForm: {
      type: Boolean,
      default: true
    },
    showTiele: {
      type: Boolean,
      default: true
    },
    showAnswer: {
      type: Boolean,
      default: false
    },
    isAnswer: {
      type: Boolean,
      default: false
    },
    isAnswerHint:{
      type: Boolean,
      default: false
    },
    readOnly:{
      type: Boolean,
      default: false
    },
    sort:{
      type: Number,
      default: 0
    },
    disabled:{
      type: Boolean,
      default: false
    },
    publishTask:{ //  是否是任务发布
      type: Boolean,
      default: false
    },
    showTitle:{ //  是否显示标题
      type: Boolean,
      default: true
    }
  },
  components: {
    'form-preview': () => import('@/components/base/question/answer-form-preview.vue'),
    'synthesisview': () => import('@/components/base/question/synthesis-view.vue'),
    'knowledge': () => import('./knowledge/index.vue')
  },
  data () {
    return {
      questionTypeLabel: questionTypeLabel,
      type: '',
      title: '',
      desc: '',
      optionConfig: {},
      answerText: '',
      answerDetail: '',
      knowledge: [],
      skill: [],
      difficulty: '',
      difficultyText: '',
      answerValue:'',// 答案的值
      showAnswerInfo:false
    }
  },
  watch: {
    data: {
      handler (val) {
        console.log(val,"--------------------this.data")
        this.initData()

      },
      deep: true,
      immediate: true
    },
    showAnswer: {
      handler (val) {
        this.showAnswerInfo=val
      },
      immediate: true
    },
    answerText: {
      handler() {
        if (this.type === 46 && this.hasClosedHtmlTags(this.answerText)) {
          this.adjustInputHeight();
        }
      },
      immediate: true
    }
  },
  created() {
    this.initData()
  },
  mounted() {

  },
  methods: {
    initData () {
      if (this.data) {
        this.type = this.data.type || ''
        this.title = this.data.title || ''
        this.desc = this.data.desc || ''
        // spread 预览
        try{
          this.optionConfig = cloneDeep(this.data.optionConfig) || {}
        }catch(e){
          delete this.data.optionConfig.spread
          this.optionConfig = cloneDeep(this.data.optionConfig) || {}
        }
        this.answerDetail = this.data.answerDetail || ''
        this.knowledge = this.data.knowledge || []
        this.skill = this.data.skill || []
        this.difficulty = this.data.difficulty || this.data.difficulty === 0 ? this.data.difficulty : ''
        this.answerText = getAnswerDispatch(this.type, this.data.optionConfig)
        this.difficultyText = getDifficultyDispatch(this.difficulty) || ''

        // 先临时这样处理,后续根据入参调整
        // if((this.$route.path=='/teachingWork'||this.$route.path=='/platform-question-manage')&&this.data.type!=5&&this.data.type!=49){
        // if((this.$route.path=='/teachingWork'||this.$route.path=='/platform-question-manage')&&(this.data.type==1||this.data.type==2||this.data.type==8)){
        //   this.answerValue = '' // 题目答案
        // }else{
          this.answerValue = this.data.answer || '' // 题目答案
        // }
        // console.log(this.data.type,"this.answerValue------------ 题目回显答案",this.answerValue)
      }
    },
    hasClosedHtmlTags,
    adjustInputHeight() {
      this.$nextTick(() => {
        if (this.$refs.answerInput) {
          const textarea = this.$refs.answerInput.$refs.textarea;
          if (textarea) {
            // 计算内容高度
            textarea.style.height = 'auto';
            textarea.style.height = `${textarea.scrollHeight + 20}px`;
          }
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
.question-preview-content {
  display: grid;
  grid-gap: 20px;
  color: #222;
  width: 100%;
  .type-text {
    border-left: 3px solid var(--theme_primary_color);
    padding-left: 10px;
    font-weight: 700;
  }
  .row {
    .row-label {
      color: #999;
      padding-bottom: 10px;
    }
     .hint-content{
      max-width: 100%;
      overflow-x: auto;
      ::v-deep code {
        display: block;
        background-color: #f4f4f4;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        font-family: 'Courier New', Courier, monospace;
        white-space: pre-wrap; /* 保留空白符序列，正常进行换行 */
        word-break: break-word; /* 允许在单词内换行 */
        overflow-wrap: break-word; /* 内容在边界内换行 */
      }
      ::v-deep table{
        border-top:1px solid #d4d4d5;
        border-right:1px solid #d4d4d5;
        border-spacing:0;
        td,th{
          border-bottom: 1px solid #d4d4d5;
          border-left: 1px solid #ccc;
          padding:4px 10px;
        }
      }
    }
    .null-data {
      &:before {
        content: '--';
        color: #CCCCCC;
        font-size: 13px;
      }
    }
    :deep(.code-area){
      min-height: 300px;
      .el-textarea__inner{
        min-height: 300px!important;
      } 
    }
  } 
  :deep(img) {
    max-width: 100%;
  }
}
</style>