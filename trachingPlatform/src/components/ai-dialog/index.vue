<template>
  <div class="task-tipa">
    <el-dialog :visible.sync="dialogVisible" title=""   :width="isWideContent ? 'auto' : '21%'" custom-class="TaskDialogAi"
      :close-on-click-modal="false" v-dialogDragTask :modal="false" :show-close="false"
      >
      <div slot="title" class="dialog-header-title">
        <div class="headerRobot">
           <img src="../../assets/discussion/rodic.gif"/>
        </div>
        <div class="s-title">AI助手</div>
        <div class="icon-titlej">
          <div class="iconc">
            <el-tooltip class="item" effect="light" content="历史记录" placement="top-start">
             <i class="iconfont icon-lishijilu iconclosehis"  @click="handleHistorySession"></i>
            </el-tooltip>
            <el-tooltip class="item" effect="light" content="新建对话" placement="top-start">
             <i class="el-icon-plus iconclose"  @click="handleClearSession"></i>
            </el-tooltip>
            <i class="el-icon-arrow-down iconzize"  @click="handlecloseAiModal"></i>
          </div>
        </div>
      </div>
      <transition name="fade">
      <div class="chatting">
        <!-- 聊天内容区域 -->
        <div ref="chattingContent" id="chattingContent" class="chatting-content">
          <div class="robot-default">
           <div class="robot-message">欢迎体验教学平台助手，我可以回答你的问题，辅助你了解平台。</div>
          
          </div>
          <div v-for="item of msgs">
            <!--用户输入内容-->
            <div v-if="item.self" class="chatting-item self clearfix">
              <div class="msg-date">
                {{ item.date }}
              </div>
              <div class="msg-from">
                <div class="msg-content">{{ item.content}}</div>
                <img :src="item.avatarUrl" alt="">
              </div>
            </div>
            <!--AI回复内容-->
            <div v-else class="chatting-item other clearfix">
              <div class="msg-date">
                {{ item.date }}
              </div>
              <div class="msg-from">
                <img :src="item.avatarUrl" alt="">
                <div class="msg-content">
                  <div class="disloading" v-if="item.loading">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dotwort">检索中...</div>
                  </div>
                  <div class="msg-reply" v-else  ref="chattingmsgContent">
                      <div v-html="renderMdText(item.content)" class="msg-markdown"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 输入区域 -->
        <div class="chatting-input">
          <div class="chatting-tl">
           <el-input type="textarea"  @keydown.native="listen($event)"  ref="count"  :disabled="isInput"  resize="none"  :rows="5" v-model="inputContent"  placeholder="输入平台疑问,与AI互动交流"/>
         </div>
        <!-- <div class="sendupload"  @click="openUpload"> <i class="el-icon-upload elsend"></i></div> -->
          <div class="endcircle"  @click="endsend" v-if="issending">
            <el-tooltip class="item" effect="light" content="停止生成" placement="top-start">
            <span class="changeCircle"></span>
            </el-tooltip>
          </div>
          <div class="sendcircle"  @click="send" v-else> <i class="el-icon-s-promotion elsend"></i></div>
        </div>
      </div>
    </transition>
    <transition name="slide">
    <div class="historyRecordmain" v-if="isshowHistory">
      <HistoryRecord ref="historyRecordref" @handlehisToryclose="handlehisToryclose" :chatSessionList="chatSessionList"
       @Getappointchatdata="Getappointchatdata"
       @handlehisToggleTop="handlehisToggleTop"
       @HandleEditChatTitle="HandleEditChatTitle"
       @HandleDelChatSession="HandleDelChatSession"
       ></HistoryRecord>
    </div>
  </transition> 
    </el-dialog>
  </div>
</template>

<script>
import HistoryRecord from './components/HistoryRecord.vue'
import axios from "axios";
import MarkdownIt from 'markdown-it'
import {requestSse} from '@/utils/requestSse'
import mk from 'markdown-it-katex'
import hljs from 'highlight.js';
import 'highlight.js/styles/default.css'
import moment from 'moment'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import aiHeadImg from '../../assets/discussion/robot.png'
import clientHeadImg from '../../assets/discussion/rome.png'
import { mapGetters } from "vuex";
const md = MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    // 当前时间加随机数生成唯一的id标识
    const codeIndex = parseInt(Date.now()) + Math.floor(Math.random() * 10000000)
    let html = `<button class="copy-btn" type="button" data-clipboard-action="copy" data-clipboard-target="#copy${codeIndex}">复制</button>`
    const linesLength = str.split(/\n/).length - 1
    // 生成行号
    let linesNum = '<span aria-hidden="true" class="line-numbers-rows">'
    for (let index = 0; index < linesLength; index++) {
      linesNum = linesNum + '<span></span>'
    }
    linesNum += '</span>'
    if (lang && hljs.getLanguage(lang)) {
      try {
        // highlight.js 高亮代码
        const preCode = hljs.highlight(lang, str, true).value
        html = html + preCode
        if (linesLength) {
          html += '<b class="name">' + lang + '</b>'
        }
        // 将代码包裹在 textarea 中
        return `<pre class="hljs"><code>${html}</code>${linesNum}</pre><textarea style="position: absolute;top: -9999px;left: -9999px;z-index: -9999;" id="copy${codeIndex}">${str}</textarea>`
      } catch (error) {
        console.log(error)
      }
    }

    const preCode = md.utils.escapeHtml(str)
    html = html + preCode
    // 将代码包裹在 textarea 中
    return `<pre class="hljs"><code>${html}</code>${linesNum}</pre><textarea style="position: absolute;top: -9999px;left: -9999px;z-index: -9999;" id="copy${codeIndex}">${str}</textarea>`
  }
  }).use(mk,{ 
  throwOnError: false,
  errorColor: ' #cc0000',
  delimiters: [
    {left: '$$', right: '$$', display: true},
    {left: '$', right: '$', display: false},
    {left: '\\(', right: '\\)', display: false},
    {left: '\\[', right: '\\]', display: true}
  ]
});
export default {
  name: "TaskDialogAi",
  components: {
    HistoryRecord
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isMove: false,
      dialogVisible: false,
      msgs: localStorage.msgs_ai && JSON.parse(localStorage.msgs_ai) || [],
      inputContent: '',
      loading:false,
      isInput:false,
      eventSource: null,
      markDownText:'',
       //初始化
       markdownRender:md,
       issending:false,
       abortController:null,
       isshowHistory:false,
       isWideContent: false,
       contentWidth: 0,
       chatuniqueId:Date.now().toString(),
       chatSessionList:[],
       chatId:0
      
    };
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.clipboard = new Clipboard('.copy-btn')
      this.clipboard.on('success', (e) => {
        this.$message.success('复制成功')
      })
      this.clipboard.on('error', (e) => {
        this.$message.error('复制成功失败')
      })
    })
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"]
    }),
  },
  methods: {
    checkContentWidth() {
    this.$nextTick(() => {
      const contentElements = document.querySelectorAll('.msg-content');
      if (contentElements.length > 0) {
        const lastContent = contentElements[contentElements.length - 1];
        this.isWideContent = lastContent.scrollWidth > 290;
      }
    });
  },
    listen(event) {
     if (event.shiftKey && event.keyCode == 13) {
        event.cancelBubble = true; //ie阻止冒泡行为
        event.stopPropagation();//Firefox阻止冒泡行为
        event.preventDefault(); //取消事件的默认动作*换行
        this.inputContent += '\n';
     } else if (event.keyCode == 13) {
        event.preventDefault(); // 阻止浏览器默认换行操作*/
        this.send()
     }
   },
    handlecloseAiModal(){
      this.$emit('handlecloseAiModal',false)
    },
    handlehisToryclose(){
      this.isshowHistory=false
    },
   async  HandleDelChatSession(val){
    let params={
        chatId:val.chatId
      }
    let {data,code}= await this.$api.HandleDeleteChatSession(params)
      if(code==200){
       this.msgs=[]
       this.chatId=0
       this.getSessionrefesh()
    }
   },
   async HandleEditChatTitle(val){
    if(val.title==''){
      this.$message.warning('请输入对话名称')
      return
     }
      let params=val
      let {data,code}= await this.$api.HandleEditChatSessionTitle(params)
      if(code==200){
        this.getSessionrefesh()
        this.$message.success('编辑成功')
        this.$refs.historyRecordref.handlecloseEditModal()

      }
    },
   async handlehisToggleTop(val){
      let params={
        chatId:val.chatId
      }
      let {data,code}= await this.$api.SetToggleTop(params)
      if(code==200){
       this.getSessionrefesh()
      }

    },
   async Getappointchatdata(val){
      this.chatId=val.chatId
      let params={
        chatId:val.chatId
      }
     let {data,code}= await this.$api.GetChatUniqueList(params)
      if(code==200){
        let msgdata = data.flatMap(item => [
          {
            date: item.createdAt,
            content: item.question,
            self: true,
            avatarUrl: clientHeadImg,
            loading: false
          },
          {
            date: item.createdAt,
            content: item.answer,
            self: false,
            avatarUrl: aiHeadImg,
            loading: false
          }
        ]);
       this.msgs= msgdata

       }
    },
   async getSessionrefesh(){
    let {data,code}= await this.$api.GetChatSessionList({})
        if(code==200){
          this.chatSessionList=data
      }
    },
   async  handleHistorySession(){
     this.isshowHistory=!this.isshowHistory
     if(this.isshowHistory){
      //let Url='http://chat.zdapwk.com:8810/close_session'
      // let Url='http://192.168.1.117:5600/get_user_history'
      // axios.post(Url, {
      //      user_id: this.userInfo.id,     
      //      page :1,
      //      page_size :10
      //     },
      //     {
      //       headers: {
      //             'Content-Type': 'application/json'
      //         } 
      //     }).then(res => {
      //      if(res.data.status=='success'){
     
      //         //this.$emit('handlecloseAiModal',false)
      //      }
      //     }).catch(err => {
      //       this.$message.info(err);
      //     })
       let {data,code}= await this.$api.GetChatSessionList({})
       if(code==200){
        this.chatSessionList=data
       }
      }
    },
    handleClearSession(){
      this.chatuniqueId=Date.now().toString()
      this.msgs=[]
      this.chatId=0
      this.isInput=false
      // let Url='http://chat.zdapwk.com:8810/close_session'
      // axios.post(Url, {
      //      user_id: this.userInfo.id,     
      //     },
      //     {
      //       headers: {
      //             'Content-Type': 'application/json'
      //         } 
      //     }).then(res => {
      //      if(res.data.status=='success'){
      //         this.msgs=[]
      //         this.isInput=false
      //      }
      //     }).catch(err => {
      //       this.$message.info(err);
      //     })
    },
    stopFetch() {
      if (this.abortController) {
         this.abortController.abort(); // 中止操作
         this.abortController = null; // 清空引用
      }
    },
    endsend(){
      if (this.abortController) {
         this.abortController.abort(); // 中止操作
         this.abortController = null; // 清空引用
      }
      this.issending=false
      this.isInput=false
      const box=this.$refs.chattingContent
      this.$nextTick(()=>{
          box.scrollTop =box.scrollHeight
      })
       clearInterval(this.timer)
      //  let Url='http://chat.zdapwk.com:8810/stop_response'
      //  this.stopFetch()
      //  axios.post(Url, {
      //      user_id: this.userInfo.id,     
      //     },
      //     {
      //       headers: {
      //             'Content-Type': 'application/json'
      //         } 
      //     }).then(res => {
      //      if(res.data.status=='success'){
      //            this.issending=false
      //            this.isInput=false
      //            const box=this.$refs.chattingContent
      //            this.$nextTick(()=>{
      //             box.scrollTop =box.scrollHeight
      //           })
      //            clearInterval(this.timer)
      //      }
      //     }).catch(err => {
      //       this.$message.info(err);
      //     })
    },
    renderMdText(text){
               //生成html
          if(text.includes('https') || text.includes('http')){
          const safeMd = new MarkdownIt({
              html: false,
              linkify: false,
              breaks: true
          });
          return safeMd.render(text);
        }else{   
          if (text.includes('_{')){  
          const normalizedText = text
          .replace(/C_(\d+)\^(\d+)=([^=]+)/g, '\\binom{$1}{$2} = $3')  // 处理单独的 C_n^k    
          .replace(/C_(\d+)\^(\d+)/g, '\\binom{$1}{$2}')   // 处理分数中的乘号 ×          
          .replace(/×/g, '\\times')
          .replace(/\\\((.+?)\\\)/g, '$$$1$$')    // \(...\) → $$...$$
          .replace(/\\\[(.+?)\\\]/g, '$$$$$1$$$$') // \[...\] → $$$$...$$$$
          .replace(/\$\$(.+?)\$\$/g, '$$$$$1$$$$') // 确保块级公式分隔符正确
          return this.markdownRender.render(normalizedText);
          }else{
            const normalizedText = text
            .replace(/C_(\d+)\^(\d+)=([^=]+)/g, '\\binom{$1}{$2} = $3')  // 处理单独的 C_n^k    
            .replace(/C_(\d+)\^(\d+)/g, '\\binom{$1}{$2}')   // 处理分数中的乘号 ×          
            .replace(/\\\((.+?)\\\)/g, '$$$1$$')    // \(...\) → $$...$$
            .replace(/\\\[(.+?)\\\]/g, '$$$$$1$$$$') // \[...\] → $$$$...$$$$
            .replace(/\$\$(.+?)\$\$/g, '$$$$$1$$$$') // 确保块级公式分隔符正确
             return this.markdownRender.render(normalizedText);
          }
        }
    },
    send() {
          if (this.inputContent === '') {
            return;
          }
          this.isInput=true
          this.msgs.push({
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: this.inputContent,
            self: true,
            avatarUrl: clientHeadImg,
          });
          let msgArr=JSON.parse(JSON.stringify(this.msgs))
          let lastIndex = -1;
          msgArr.forEach((element,index)=> {
            if(element.self === true){
              lastIndex = index;
            }
          });
          this.msgs=msgArr
          const box=this.$refs.chattingContent
          this.$nextTick(()=>{
            box.scrollTop =box.scrollHeight
          })
          this.loading=true
          this.checkContentWidth();
          this.connectToSSE(this.inputContent)
        },
     async connectToSSE(inputContent) {
        let that=this
        that.scrollTop()
        that.inputContent=''
        that.msgs.push({
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: '',
            self: false,
            avatarUrl: aiHeadImg,
            loading:true
        })
        let params={
          // user_input: inputContent,
          // userid: this.userInfo.id
           chatId:this.chatId !==0?this.chatId:this.chatuniqueId,
           message: inputContent
        }
        let Url='http://vcsc.365ysx.com:18907/dfs/AIAgent/chatStream'
        let Urltest='http://192.168.1.170:9200/dfs/AIAgent/chatStream'
        //let Url='http://192.168.1.117:5600/chat'
        //this.abortController = new AbortController();     
        // fetchEventSource(Url, {
        //   method: 'POST',
        //   headers: {
        //     "Content-Type": 'application/json',
        //     "Accept": 'text/event-stream'
        //   },
        //   body: JSON.stringify(data),
        //   openwhenHidden:true,
        //   signal: this.abortController.signal,
        //   onmessage(event) {
        //     that.issending=true
        //     const res = JSON.parse(event.data);
        //     let msgArr=JSON.parse(JSON.stringify(that.msgs))
        //     msgArr[msgArr.length - 1].loading= false
        //     msgArr[msgArr.length - 1].content += res.response !== null?res.response:'抱歉，这些问题我无法回答。'
        //     that.msgs=msgArr    
        //     that.checkContentWidth();
        //   },
        //   onclose(e) {
        //     that.issending=false
        //     const box=that.$refs.chattingContent
        //     that.$nextTick(()=>{
        //       box.scrollTop =box.scrollHeight
        //     })
        //     that.isInput=false
        //     clearInterval(that.timer)
        //       // 关闭流
        //   },
        //   onerror(error) {
        //     throw Error(error);
        //       //返回流报错
        //   }
        // })
        this.abortController = new AbortController();
        await requestSse(Url, params, this.abortController, (data) => {
            if (!this.abortController?.signal.aborted) {
            that.issending=true
            let msgArr=JSON.parse(JSON.stringify(that.msgs))
            msgArr[msgArr.length - 1].loading= false
            msgArr[msgArr.length - 1].content += data.content !== null?data.content:'抱歉，这些问题我无法回答。'
            that.msgs=msgArr    
            that.checkContentWidth(); 
            }else{
              that.issending=false
              const box=that.$refs.chattingContent
              that.$nextTick(()=>{
                box.scrollTop =box.scrollHeight
              })
              that.isInput=false
              clearInterval(that.timer)
            }    
            if(data.type=='close'){
              that.issending=false
              const box=that.$refs.chattingContent
              that.$nextTick(()=>{
                box.scrollTop =box.scrollHeight
              })
              that.isInput=false
              clearInterval(that.timer)
              if(this.chatId==0){
                that.getSessionrefesh()
              }
            }
      })
      },
    beforeDestroy() {
        if (this.eventSource) {
          this.eventSource.close();
        }
    },
    scrollTop() {
        this.$nextTick(() => {
          this.timer = setInterval(() => {
              const box=this.$refs.chattingContent
              if(box){
              box.scrollTop =box.scrollHeight
              }
          }, 1300)
        })
    }
  },
  watch: {
    isOpen: {
      handler(n, o) {
        this.dialogVisible = n;
      },
      immediate: true,
      deep: true,
    },
    // msgs(val) {
    //       localStorage.msgs_ai = JSON.stringify(val);
    // }
  },
};
</script>

<style lang="scss">
@import '~katex/dist/katex.min.css';
.task-tipa .el-dialog__wrapper {
  position: absolute;
  z-index: 100;
  top: auto;
  right: auto;
}
.task-tipa .el-dialog__footer {
  padding: 0;
}
.TaskDialogAi {
  min-width: 21%;
  max-width: 930px;
  margin: 0;
  position: fixed;
  z-index: 100;
  right: 130px;
  bottom: 60px;
  height: calc(100% - 180px);
  display: flex;
  flex-direction: column;
  font-size: 14px;
  background:#E7F2FF;
  border: 2px solid transparent;
  border-radius: 5px;
  background-image: linear-gradient(#fff,#fff),
  linear-gradient(180deg, rgba(106, 132, 255, 1),rgba(174, 85, 255, 1));
  background-origin:border-box;
  background-clip:content-box,border-box;
  margin-top: 0 !important;
  box-shadow: 0px 2px 16px 0px rgba(0, 0, 0, 0.16);
  // border-image: linear-gradient(135deg, rgba(106, 132, 255, 1), rgba(174, 85, 255, 1)) 1;
  // clip-path:inset(0 round 2px);
    // .robotcover{
    //   position: absolute;
    //   width: 100%;
    //   height: 167px;
    //   bottom: 0;
    //   background: url('../../assets/discussion/robotcover.png') no-repeat;
    //   background-size:100% 100%;
    // }
  .el-dialog__title{
    line-height: 41px;
    color: #ffffff;
  }
  .el-dialog__header{
   padding: 13px 14px 13px 14px ;
   border-bottom: 1px solid #C3DDFF;
   background-color: #E7F2FF;
   border-top-left-radius:4px ;
   border-top-right-radius:4px ;
  }
  .dialog-header-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .headerRobot{
        position: absolute;
        top:-13px;
        left: 50%;
        transform:translate(-50%, -50%);
        width: 58px;
        height: 58px;
        border-radius: 50%;
        img{
          width: 100%;
          height: 58px;
        }
      }
      .s-title{
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
      }
      .icon-titlej {
        display: flex;
        .iconc {
          cursor: pointer;

          .iconzize {
            font-size: 22px;
            color:#98b4dc;
          }
          .iconclosehis {
            font-size: 17px;
            color:#98b4dc;
            padding-right: 9px;
          }
          .iconclose {
            font-size: 20px;
            color:#98b4dc;
            padding-right: 5px;
          }
        }

      }
    }

  .el-dialog__body {
    padding: 0;
    height: calc(100% - 50px);
    display: flex;
    width: 100%;
    // position: relative;
  }
  .chatting {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      // background:url('../../assets/discussion/robotcover.png') no-repeat ;
      // background-size: auto 160px;
      // background-position:bottom;
      .robot-default{
        width: 100%;
        padding: 12px 18px 15px 18px;
        .robot-message{
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
        }
        .robotList{
          width: 100%;
          //min-height: 198px;
          height: auto;
          background: #fff;
          border-radius: 8px;
          margin-top: 16px;
          padding: 15px 15px 10px 15px;
          .robotlabel{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
          }
          .robotmian{
            padding-top: 10px;
            .robotItem{
              width: 100%;
              height: auto;
              background: #F2F6FF;
              border-radius: 4px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #4A6EA1;
              display: flex;
              align-items: center;
              padding-left: 15px;
              cursor: pointer;
              line-height: 21px;
              padding: 6px 12px 6px 16px;
            }
          }
        }
      }
      .chatting-content {
        width: 100%;
        height: 100%;
        background: linear-gradient( 180deg, #E7F2FF 0%, #F4F1FF 100%);
        box-shadow: 0px 0px 7px 0px rgba(0,0,0,0.11);
        overflow: auto;
        padding-bottom: 15px;
        .chatting-item {
          padding: 10px;
          width: 100%;
          .msg-date {
            text-align: center;
            color: gray;
            font-size: 80%;
          }
          .msg-from {
            display: flex;
            span.loc {
              color: gray;
              font-size: 60%;
              margin-right: 5px;
            }
            .msg-author {
              font-size: 1.2rem;
            }
            img {
              width: 38px;
              height: 38px;
              border-radius: 15px;
            }
          }
          .msg-content {
            margin-top: 5px;
            background-color: white;
            // max-width: 300px;
            padding: 8px 10px 8px 12px;
            border-radius: 8px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 23px;
            white-space: pre-line
          }
        }

        .chatting-item + .chatting-item {
          margin-top: 10px;
        }
      //   .msgcontent{
      //     width: 100%;
      //     display: flex;
      //     justify-content: flex-end;
      //    .msgFile{
      //     display: flex;
      //     // width: 50%;
      //     flex-direction: column;
      //     justify-content: flex-end;
      //     padding-right: 50px;
      //     .filelist{
      //        margin-bottom: 8px;
      //        .flieimg{
      //         display: flex;
      //         justify-content: flex-end;
      //         img{
      //           width: 100px;
      //           height: 100px;
      //         }
      //        }
      //        .flieitemname{
      //         padding: 4px 10px;
      //         background-color: #fff;
      //         border-radius: 8px;
      //         text-align: center;
      //         a{
      //           color: #4A6EA1;
      //           font-size: 14px;
      //           height: 20px;

      //         }
      //       }
      //     }

      //    }
      // }
        .self {
          .msg-from {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            img {
              margin-left: 10px;
            }
            .msg-content {
             margin-left: 49px;
             padding: 8px 8px 8px 12px;
             font-size: 16px;
             line-height: 25px;
            }
          }

          .msg-content {
            float: right;
            word-wrap: break-word;
            word-break: break-all;
          }
        }
        .other {
          .msg-from {
            display: flex;
            justify-content: flex-start;
            width: calc(100% - 45px);
            img {
              margin-right: 10px;
            }
          }

          .msg-content {
            float: left;
            word-wrap: break-word;
            word-break: break-all;
            //white-space: pre-line‌;
            .msg-reply{
              white-space:normal;
              .msg-markdown{
               // width: calc(100% - 45px);
               font-size: 16px;
               line-height: 25px;
              }
      pre.hljs {
      padding: 12px 2px 12px 40px !important;
      border-radius: 5px !important;
      position: relative;
      font-size: 14px !important;
      line-height: 22px !important;
      overflow: hidden !important;
      margin-top: 8px;
      code {
        display: block !important;
        margin: 17px 10px !important;
        overflow-x: auto !important;
      }
      .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 30px;
        bottom: 12px;
        left: 0;
        font-size: 100%;
        width: 40px;
        text-align: center;
        letter-spacing: -1px;
        border-right: 1px solid #999999;
        user-select: none;
        counter-reset: linenumber;
        span {
          pointer-events: none;
          display: block;
          counter-increment: linenumber;
          &:before {
            content: counter(linenumber);
            color: #999;
            display: block;
            text-align: center;
          }
        }
      }
      b.name {
        position: absolute;
        top: 2px;
        left: 10px;
        z-index: 10;
        color: #999;
        pointer-events: none;
      }
      .copy-btn {
        position: absolute;
        top: 7px;
        right: 10px;
        z-index: 10;
        color: #333;
        cursor: pointer;
        background-color: #fff;
        border: 0;
        border-radius: 2px;
      }
    }

            }
          }

        }

        .online {
          width: 200px;
          // max-width: 100%;
          margin: 3px auto;
          border-radius: 4px;
          text-align: center;
          background-color: #FFFDE7;
        }
 
        .disloading{
          width: 100%;
          display: flex;
          align-items: center;

           .dot {
              position: relative;
              border-radius: 50%;
              margin-right:7px;
              width: 4px;
              height: 4px;
           }
           @keyframes waveing { 50%, 57% { /*放大*/ transform: scale(1.7); } 80%, 100% { /*透明*/ opacity: 1; }}
           .dot::before {
               position: absolute;
               content: "";
                width: 100%;
                height: 100%;
                background: inherit;
                border-radius: inherit;
                /*无限执行自定义的wave水波纹动画*/
                animation: waveing 0.6s ease-out infinite;
            }
            .dot:nth-child(1) { background: #6a84ff;}
            .dot:nth-child(1)::before {animation-delay: 0.2s;}
            .dot:nth-child(2) { background: #6a84ff;}
            .dot:nth-child(2)::before { animation-delay: 0.3s}
            .dot:nth-child(3) { background: #6a84ff;}
            .dot:nth-child(3)::before { animation-delay: 0.4s}
            .dotwort{
              padding-left: 4px;
              color: #333;
              font-size: 13px;
           }
        }
   
      }
      .chatting-input {
        height: auto;
        width: 100%;
        position: relative;
        padding: 10px 20px 20px 20px;
        background-color: #f1f1ff;
        border-bottom-left-radius:4px ;
        border-bottom-right-radius:4px ;
        border: 1px solid #F0F0F0;
        .chatting-tl{
          width: 100%;
        //   .el-input--medium .el-input__inner{
        //   height: 40px;
        //   line-height: 40px;
        // }
        .el-textarea__inner{
          border: none;
          border-radius: 8px;
          // resize: none;
        }
        }

          // .robotbtn{
          //   width: 80px;
          //   height: 40px;
          //   background-color: #2B66FF;
          //   color: #ffffff;
          // }
          .sendcircle{
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #E4EAFF;
            text-align: center;
            line-height: 39px;
            position: absolute;
            bottom: 30px;
            right: 31px;
            cursor: pointer;
            .elsend{
             font-size: 20px;
             color:#ae55ff ;
             &:hover{
              color:rgb(106, 132, 255);
             }
            }
          
         }
          .endcircle{
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #E4EAFF;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            bottom: 30px;
            right: 31px;
            cursor: pointer;
            .changeCircle{
              width: 14px;
              height: 14px;
              background-color:#ae55ff;
              border-radius: 2px;
              display: block;
              &:hover{
              background-color:rgb(106, 132, 255);
             }
            }
          
         }
          .sendupload{
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #E4EAFF;
            text-align: center;
            line-height: 39px;
            position: absolute;
            bottom: 30px;
            right: 72px;
            cursor: pointer;
            .elsend{
             font-size: 20px;
             color:#ae55ff ;
             &:hover{
              color:rgb(106, 132, 255);
             }
            }

        }

      }
    }  
    ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
          }
          /* 滚动槽 */
          ::-webkit-scrollbar-track {
             background: linear-gradient(180deg, rgba(106, 132, 255, 1),rgba(174, 85, 255, 1));
             box-shadow: none;
          }
          /* 滚动条滑块 */
          ::-webkit-scrollbar-thumb {
            border-radius: 4px;
            background: linear-gradient(180deg, rgb(106, 132, 255,.6), rgb(174, 85, 255,.6));
            box-shadow: inset 0 0 1px rgba(231, 231, 231, 0.5);
          }
      .historyRecordmain{
        width: 380px;
        height: 100%;
        position: absolute;
        top: 0;
        left:-382px ;
        background: #FFFFFF;
        box-shadow: 0px 0px 8px 1px rgba(106, 132, 255,0.11);
        border-radius: 3px;
      }    
      .slide-enter-active, .slide-leave-active {
        transition: all 0.3s ease;
      }

      .slide-enter, .slide-leave-to {
        transform: translateX(100%);  // 从右向左滑动
      }

      .slide-enter-to, .slide-leave {
        transform: translateX(0);
      }
}

.katex .vlist {
      display: table-cell;
      position: relative;
      vertical-align:sub !important
 }

</style>
