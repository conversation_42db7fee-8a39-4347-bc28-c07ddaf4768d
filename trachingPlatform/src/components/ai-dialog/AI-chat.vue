<template>
  <div class="Ai-page">
    <!-- 主内容区域 -->
    <div class="Ai-content"> <!-- 历史会话容器 - 默认隐藏 -->
      <div class="history-container" :class="{ 'show': showHistoryPanel }">
        <div class="history-header">
          <h3>AI助手</h3>
          <div class="history-toggle-btn" @click="showHistoryPanel = false">
            <i class="iconfont icon-a-yousuojin3xbeifen"></i>
          </div>
        </div>
        <div class="history-list">
          <!-- 今天的会话 -->
          <div class="history-group">
            <div class="history-group-title">今天</div>
            <div v-for="(item, index) in todaySessions" :key="'today-' + index" class="history-item" :class="{ active: isActiveSession(item.id) }" @click="loadSession(item.id)">
              <div v-if="editingSessionId === item.id && editingCategory === 'today' && editingIndex === index" class="history-item-edit">
                <input v-model="editingTitle" type="text" class="edit-input" @blur="saveEditSession" @keyup.enter="saveEditSession" @keyup.esc="cancelEditSession" ref="editInput"/>
              </div>
              <div v-else class="history-item-content">{{ item.content }}</div>
              <div class="history-item-time">{{ item.time }}</div>
              <i class="iconfont icon-shanchu history-delete-btn" @click.stop="deleteSession(item.id, 'today', index)" title="删除会话"></i>
              <i class="iconfont icon-bianji history-edit-btn" @click.stop="startEditSession(item.id, 'today', index, item.content)" title="编辑会话标题"></i>
            </div>
          </div>
          
          <!-- 最近7天的会话 -->
          <div class="history-group">
            <div class="history-group-title">最近7天</div>
            <div v-for="(item, index) in recentSessions" :key="'recent-' + index" class="history-item" :class="{ active: isActiveSession(item.id) }" @click="loadSession(item.id)">
              <div v-if="editingSessionId === item.id && editingCategory === 'recent' && editingIndex === index" class="history-item-edit">
                <input v-model="editingTitle" type="text" class="edit-input" @blur="saveEditSession" @keyup.enter="saveEditSession" @keyup.esc="cancelEditSession" ref="editInput"/>
              </div>
              <div v-else class="history-item-content">{{ item.content }}</div>
              <div class="history-item-time">{{ item.time }}</div>
              <i class="iconfont icon-shanchu history-delete-btn" @click.stop="deleteSession(item.id, 'recent', index)" title="删除会话"></i>
              <i class="iconfont icon-bianji history-edit-btn" @click.stop="startEditSession(item.id, 'recent', index, item.content)" title="编辑会话标题"></i>
            </div>
          </div>
          
          <!-- 更早的会话 -->
          <div class="history-group">
            <div class="history-group-title">更早</div>
            <div v-for="(item, index) in earlierSessions" :key="'earlier-' + index" class="history-item" :class="{ active: isActiveSession(item.id) }" @click="loadSession(item.id)">
              <div v-if="editingSessionId === item.id && editingCategory === 'earlier' && editingIndex === index" class="history-item-edit">
                <input v-model="editingTitle" type="text" class="edit-input" @blur="saveEditSession" @keyup.enter="saveEditSession" @keyup.esc="cancelEditSession" ref="editInput"/>
              </div>
              <div v-else class="history-item-content">{{ item.content }}</div>
              <div class="history-item-time">{{ item.time }}</div>
              <i class="iconfont icon-shanchu history-delete-btn" @click.stop="deleteSession(item.id, 'earlier', index)" title="删除会话"></i>
              <i class="iconfont icon-bianji history-edit-btn" @click.stop="startEditSession(item.id, 'earlier', index, item.content)" title="编辑会话标题"></i>
            </div>
          </div>
        </div>
        <div class="history-footer">
          <span class="history-tip">历史对话支持最近3个月记录</span>
        </div>
      </div>
    
      <!-- 展开历史会话按钮 - 显示在主内容区域左上角 -->
      <div class="history-toggle-btn-main" @click="getHistoryList" :style="showHistoryPanel ? 'display: none' : ''">
        <i class="iconfont icon-a-ziyuan11"></i>
        历史会话
      </div>
        <div class="ai-tip-box">
          <img src="@/assets/public/ai-icon.png" alt="">
          <div class="tip-label">
            <h4> {{type === 'teacher' ? '老师' : '学生'}}，您好！我是您的课程AI小助手</h4>
            <p>我可以帮您做这些事情  <span>换一换 <i class="iconfont icon-a-ziyuan16"></i></span></p>
          </div>
        </div>
        <div v-if="messageList.length === 0" class="ai-default-tip">
          <el-card class="box-card card-1">
            <div slot="header" class="clearfix1">
              <p> {{type === 'teacher' ? '教师' : '学生'}}常问  <span>为您推荐课程相关的问题</span></p>
            </div>
            <div v-for="o in 4" :key="o" class="text card-item">
              <p class="item-tip">{{'如果学习应付股利这个知识点 ' + o }}</p>
            </div>
          </el-card>

          <el-card class="box-card card-2">
            <div slot="header" class="clearfix2">
              <p>效率工具  <span>快来试试您可能会常用的AI工具吧</span></p>
            </div>
            <div v-for="o in 2" :key="o" class="text card-item">
              <span class="item-icon"> <i class="iconfont icon-ai-assistant-fill"></i></span>
              <div class="right-content">
                <p class="up-tip">AI答疑</p>
                <p class="sub-tip">根据关键词答疑解惑…</p>
              </div>
            </div>
          </el-card>
        </div>

        <div ref="chattingContent" id="chattingContent" class="chatting-content" v-if="messageList.length > 0">
          <div v-for="item of messageList" :key="item.chatuniqueId">
            <!--用户输入内容-->
            <div v-if="item.self" class="chatting-item self clearfix">
              <div class="msg-date">
                {{ item.date }}
              </div>
              <div class="msg-from">
                <div class="msg-content" v-html="convertNewlines(item.content)"></div>
                <div class="avatar-container">我</div>
              </div>
            </div>
            <!--AI回复内容-->
            <div v-else class="chatting-item other clearfix">
              <div class="msg-date">
                {{ item.date }}
              </div>
              <div class="msg-from">
                <img src="@/assets/public/ai-icon.png" alt="AI头像">
                <div class="msg-content">
                  <div class="disloading" v-if="item.loading">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dotwort">检索中...</div>
                  </div>
                  <div class="msg-reply" v-else  ref="chattingmsgContent">
                      <!-- 显示深度思考内容 -->
                      <div v-if="item.thinkingContent" class="thinking-content">
                        <div v-html="renderMdText(item.thinkingContent)" class="thinking-markdown"></div>
                      </div>
                      <div v-html="renderMdText(item.content)" class="msg-markdown"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-box" :style="messageList.length > 0 ? 'margin-top: 30px' : ''">
          <el-input class="chat-input" :disabled="isInput" maxlength="1000"  type="textarea" placeholder="试着输入您想了解的问题吧。输入shift+enter 换行" v-model="inputValue" @keyup.enter="handleSend"></el-input>
          <div class="chat-btn-box">
            <el-button :class="['deep-think-btn', { 'thinking': isDeepThinking }]" @click="handleDeepThink">
              <i class="iconfont icon-sikaodian"></i>&nbsp;深度思考
            </el-button>
            <el-button class="send-btn " @click="handleSend"><i class="iconfont icon-send-s"></i></el-button>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import {requestSse} from '@/utils/requestSse'
import MarkdownIt from 'markdown-it'
import mk from 'markdown-it-katex'
import hljs from 'highlight.js';
import 'highlight.js/styles/default.css'

const md = MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    // 当前时间加随机数生成唯一的id标识
    const codeIndex = parseInt(Date.now()) + Math.floor(Math.random() * 10000000)
    let html = `<button class="copy-btn" type="button" data-clipboard-action="copy" data-clipboard-target="#copy${codeIndex}">复制</button>`
    const linesLength = str.split(/\n/).length - 1
    // 生成行号
    let linesNum = '<span aria-hidden="true" class="line-numbers-rows">'
    for (let index = 0; index < linesLength; index++) {
      linesNum = linesNum + '<span></span>'
    }
    linesNum += '</span>'
    if (lang && hljs.getLanguage(lang)) {
      try {
        // highlight.js 高亮代码
        const preCode = hljs.highlight(lang, str, true).value
        html = html + preCode
        if (linesLength) {
          html += '<b class="name">' + lang + '</b>'
        }
        // 将代码包裹在 textarea 中
        return `<pre class="hljs"><code>${html}</code>${linesNum}</pre><textarea style="position: absolute;top: -9999px;left: -9999px;z-index: -9999;" id="copy${codeIndex}">${str}</textarea>`
      } catch (error) {
        console.log(error)
      }
    }

    const preCode = md.utils.escapeHtml(str)
    html = html + preCode
    // 将代码包裹在 textarea 中
    return `<pre class="hljs"><code>${html}</code>${linesNum}</pre><textarea style="position: absolute;top: -9999px;left: -9999px;z-index: -9999;" id="copy${codeIndex}">${str}</textarea>`
  }
  }).use(mk,{ 
  throwOnError: false,
  errorColor: ' #cc0000',
  delimiters: [
    {left: '$$', right: '$$', display: true},
    {left: '$', right: '$', display: false},
    {left: '\(', right: '\)', display: false},
    {left: '\[', right: '\]', display: true}
  ]
});


export default {
    props:{
      type:{
        type:String,
        default:'teacher'
      }
    },
    data() {
        return {
          isDeepThinking: false,
          isThinkingMode: false, // 深度思考状态标识
          isInput:false,
          loading:false,// 对话loading
          messageList:[],// 对话记录列表
          issending:false,// 对话状态
          chatId:0,
          chatuniqueId: Date.now().toString(),
          inputValue:'',
          abortController:null,
          markdownRender:md,
          markDownText:'',
          showHistoryPanel: false, // 历史会话面板显示状态
          
          // 模拟历史会话数据 - 用于展示效果
          todaySessions: [
            
          ],
          recentSessions: [
            
          ],
          earlierSessions: [
            
          ],
          // 编辑相关状态
          editingSessionId: null,
          editingCategory: null,
          editingIndex: null,
          editingTitle: ''
        }
    },
    mounted(){

    },
    methods: {
      // 检查内容宽度
      checkContentWidth(){

      },
      // 转换文本中的换行符为HTML的br标签
      convertNewlines(text) {
        if (!text) return '';
        return text.replace(/\n/g, '<br/>');
      },
      // 获取历史会话列表
      getHistoryList(){
        this.getSessionrefesh()
      },
      handleDeepThink() {
        this.isDeepThinking = !this.isDeepThinking;
        // 这里可以添加深度思考的相关逻辑
      },
      handleSend(){
       if (this.inputValue === '') {
          return this.$message({
            message: '请输入内容',
            type: 'warning'
          });
        }
        this.isInput=true
        this.messageList.push({
          date: moment().format('YYYY-MM-DD HH:mm:ss'),
          content: this.inputValue,
          self: true,
          avatarUrl: '',
        });
        let msgArr=JSON.parse(JSON.stringify(this.messageList))
        let lastIndex = -1;
        msgArr.forEach((element,index)=> {
          if(element.self === true){
            lastIndex = index;
          }
        });
        this.messageList=msgArr
        const box = this.$refs.chattingContent
        this.$nextTick(()=>{
          box.scrollTop = box.scrollHeight
        })
        this.loading=true
        // this.checkContentWidth();
        this.connectToSSE(this.inputValue)
      },
      async connectToSSE(inputValue) {
        let that = this
        that.scrollTop()
        that.inputValue=''
        this.isThinkingMode = false // 重置深度思考状态标识
        that.messageList.push({
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: '',
            self: false,
            avatarUrl: '',
            loading:true
        })
        let params={
          // user_input: inputValue,
          // userid: this.userInfo.id
           chatId:this.chatId !==0?this.chatId:this.chatuniqueId,
           message: inputValue,
           enableThinking: this.isDeepThinking, // 是否深度思考
        }
        this.chatId == params.chatId;
        let Url=`${window.PUBLICHOSTA}AIAgent/ChatStream`
        // let Urltest='http://*************:9200/dfs/AIAgent/chatStream'
        this.abortController = new AbortController();
        await requestSse(Url, params, this.abortController, (data) => {
          if (!this.abortController?.signal.aborted) {
            that.issending = true
            let msgArr = JSON.parse(JSON.stringify(that.messageList))
            const lastMsg = msgArr[msgArr.length - 1];
            msgArr[msgArr.length - 1].loading = false
            
            const content = data.content !== null ? data.content : '抱歉，这些问题我无法回答。';
            
            // 检查是否包含思考标签
            if (content.includes('<think>')) {
              that.isThinkingMode = true;
            }
            if (content.includes('</think>')) {
              that.isThinkingMode = false;
            }
            
            // 根据状态标识分离内容
            if (that.isThinkingMode) {
              // 深度思考模式：所有内容都是思考内容
              let thinkingText = content;
              
              // 移除开始标签
              if (content.includes('<think>')) {
                thinkingText = content.split('<think>')[1] || '';
              }
              
              // 如果包含结束标签，分离思考内容和后续内容
              if (content.includes('</think>')) {
                const parts = thinkingText.split('</think>');
                thinkingText = parts[0] || '';
                
                // 结束标签后的内容作为普通回复
                const normalContent = parts[1] || '';
                if (normalContent) {
                  msgArr[msgArr.length - 1].content += normalContent;
                }
              }
              
              // 添加思考内容
              if (thinkingText) {
                if (!lastMsg.thinkingContent) {
                  lastMsg.thinkingContent = '';
                }
                lastMsg.thinkingContent += thinkingText;
              }
            } else {
              // 普通回复模式：直接添加到回复内容
              let normalContent = content;
              
              // 如果包含开始标签，分离普通内容和思考内容
              if (content.includes('<think>')) {
                const parts = content.split('<think>');
                normalContent = parts[0] || '';
                
                // 开始标签后的内容作为思考内容
                const thinkingContent = parts[1] || '';
                if (thinkingContent) {
                  if (!lastMsg.thinkingContent) {
                    lastMsg.thinkingContent = '';
                  }
                  lastMsg.thinkingContent += thinkingContent;
                }
              }
              
              // 添加普通回复内容
              if (normalContent) {
                msgArr[msgArr.length - 1].content += normalContent;
              }
            }
            
            that.messageList = msgArr;
            that.checkContentWidth(); 
          } else {
              that.issending = false
              const box = that.$refs.chattingContent
              that.$nextTick(()=>{
                box.scrollTop =box.scrollHeight
              })
              that.isInput=false
              clearInterval(that.timer)
            }    
            if(data.type=='close'){
              that.issending=false
              const box=that.$refs.chattingContent
              that.$nextTick(()=>{
                box.scrollTop =box.scrollHeight
              })
              that.isInput=false
              clearInterval(that.timer)
              // if(this.chatId==0){
              //   that.getSessionrefesh()
              // }
            }
        })
      },
      renderMdText(text){
        //生成html
        if(text.includes('https') || text.includes('http')){
          const safeMd = new MarkdownIt({
              html: false,
              linkify: false,
              breaks: true
          });
          return safeMd.render(text);
        }else{   
          if (text.includes('_{')){  
          const normalizedText = text
          .replace(/C_(\d+)\^(\d+)=([^=]+)/g, '\\binom{$1}{$2} = $3')  // 处理单独的 C_n^k    
          .replace(/C_(\d+)\^(\d+)/g, '\\binom{$1}{$2}')   // 处理分数中的乘号 ×          
          .replace(/×/g, '\\times')
          .replace(/\\\((.+?)\\\)/g, '$$$1$$')    // \(...\) → $$...$$
          .replace(/\\\[(.+?)\\\]/g, '$$$$$1$$$$') // \[...\] → $$$$...$$$$
          .replace(/\$\$(.+?)\$\$/g, '$$$$$1$$$$') // 确保块级公式分隔符正确
          return this.markdownRender.render(normalizedText);
          }else{
            const normalizedText = text
            .replace(/C_(\d+)\^(\d+)=([^=]+)/g, '\\binom{$1}{$2} = $3')  // 处理单独的 C_n^k    
            .replace(/C_(\d+)\^(\d+)/g, '\\binom{$1}{$2}')   // 处理分数中的乘号 ×          
            .replace(/\\\((.+?)\\\)/g, '$$$1$$')    // \(...\) → $$...$$
            .replace(/\\\[(.+?)\\\]/g, '$$$$$1$$$$') // \[...\] → $$$$...$$$$
            .replace(/\$\$(.+?)\$\$/g, '$$$$$1$$$$') // 确保块级公式分隔符正确
             return this.markdownRender.render(normalizedText);
          }
        }
      },
      // 获取会话列表
      async getSessionrefesh(){
        let {data,errCode}= await this.$api.ChatSessionList({})
          if(errCode==0){
            console.log(data)
            let list = data;
            // 清空分类数据
            this.todaySessions = [];
            this.recentSessions = [];
            this.earlierSessions = [];
            
            if(list && list.length > 0){
              const today = new Date();
              const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
              const sevenDaysAgo = new Date(todayStart.getTime() - 7 * 24 * 60 * 60 * 1000);
              list.forEach(item => {
                // 检查是否有 lastUpdateTime 属性
                if(!item.lastUpdateTime) return;
                
                const itemDate = new Date(item.lastUpdateTime);
                const itemStartTime = new Date(itemDate.getFullYear(), itemDate.getMonth(), itemDate.getDate());
                
                // 格式化时间显示
                const formattedTime = this.formatSessionTime(itemDate);
                
                // 创建会话项数据
                const sessionItem = {
                  content: item.title || '无标题会话',
                  time: formattedTime,
                  id: item.chatId
                };
                
                // 分类数据
                if(itemStartTime >= todayStart) {
                  // 今天的会话
                  this.todaySessions.push(sessionItem);
                } else if(itemStartTime >= sevenDaysAgo) {
                  // 7天内的会话
                  this.recentSessions.push(sessionItem);
                } else {
                  // 更早的会话
                  this.earlierSessions.push(sessionItem);
                }
              });
              
              // 按时间倒序排列
              this.todaySessions.sort((a, b) => b.time.localeCompare(a.time));
              this.recentSessions.sort((a, b) => b.time.localeCompare(a.time));
              this.earlierSessions.sort((a, b) => b.time.localeCompare(a.time));
            }
            
            // 记录最新的chatId
            if(list && list.length > 0) {
              this.chatId = list[0].chatId;
            }

            this.showHistoryPanel = true;
          }
        },
        
        // 格式化会话时间显示
        formatSessionTime(date) {
          const today = new Date();
          const itemDate = new Date(date);
          
          // 检查是否是今天
          const isToday = itemDate.toDateString() === today.toDateString();
          if(isToday) {
            // 今天的显示具体时间
            return `${itemDate.getHours().toString().padStart(2, '0')}:${itemDate.getMinutes().toString().padStart(2, '0')}`;
          }
          
          // 检查是否是今年
          const isThisYear = itemDate.getFullYear() === today.getFullYear();
          if(isThisYear) {
            // 今年的显示月日
            return `${(itemDate.getMonth() + 1).toString().padStart(2, '0')}-${itemDate.getDate().toString().padStart(2, '0')}`;
          }
          
          // 其他情况显示完整年月日
          return `${itemDate.getFullYear()}-${(itemDate.getMonth() + 1).toString().padStart(2, '0')}-${itemDate.getDate().toString().padStart(2, '0')}`;
        },
        
        // 加载选中的会话内容
        async loadSession(sessionId) {
          if (!sessionId) return;
          // 实际API调用逻辑
          try {
            // 显示加载状态
            this.loading = true;
            
            // 调用API获取会话详情
            // 注意：这里的API名称需要根据实际情况调整
            const { data, errCode } = await this.$api.ChatSessionGetMessagesByChatId({ chatId:sessionId });
            
            if (errCode == 0 && data&&data.data.length>0) {
              // 清空当前消息列表
              this.messageList = [];
              // 将API返回的消息转换为前端显示的格式
              data.data.forEach(msg => {
                this.messageList.push({
                  date: msg.createdAt || new Date().toISOString(),
                  content: msg.question,
                  self: true, // 假设user角色是用户自己
                  avatarUrl: '',
                  loading: false
                });
                this.messageList.push({
                  date: msg.createdAt || new Date().toISOString(),
                  content: msg.answer,
                  self: false, // 假设user角色是用户自己
                  avatarUrl: '',
                  loading: false
                });
              });
              
              // 设置当前会话ID
              this.chatId = sessionId;
              
              // 滚动到底部
              this.$nextTick(() => {
                const box = this.$refs.chattingContent;
                if (box) {
                  box.scrollTop = box.scrollHeight;
                }
              });
            } else {
              this.$message.warning('未找到会话内容');
            }
          } catch (error) {
            console.error('加载会话失败:', error);
            this.$message.error('加载会话失败，请重试');
          } finally {
            // 隐藏加载状态
            this.loading = false;
          }
        },
        // 根据会话ID判断是否为激活状态
        isActiveSession(sessionId) {
          return this.chatId === sessionId;
        },
        
        // 删除会话
        async deleteSession(sessionId, category, index) {
          if (!sessionId) return;
          
          // 显示确认对话框
          this.$confirm('确定要删除此会话记录吗？删除后无法恢复。', '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            try {
              // 显示加载状态
              this.loading = true;
              
              // 调用API删除会话
              // 注意：这里的API名称需要根据实际情况调整
              const { errCode, msg } = await this.$api.ChatSessionDelete({ chatId: sessionId });
              
              if (errCode == 0) {
                // 从本地数据中删除对应的会话
                switch(category) {
                  case 'today':
                    this.todaySessions.splice(index, 1);
                    break;
                  case 'recent':
                    this.recentSessions.splice(index, 1);
                    break;
                  case 'earlier':
                    this.earlierSessions.splice(index, 1);
                    break;
                }
                // 如果删除的是当前激活的会话，清空当前会话ID和聊天记录
                if (this.chatId == sessionId) {
                  this.chatId = '';
                  this.messageList = []; // 清空聊天记录
                }
                console.log('删除会话', sessionId, this.chatId, index);
                
                this.$message.success('会话删除成功');
              } else {
                this.$message.error(msg || '会话删除失败');
              }
            } catch (error) {
              console.error('删除会话失败:', error);
              this.$message.error('删除会话失败，请重试');
            } finally {
              // 隐藏加载状态
              this.loading = false;
            }
          }).catch(() => {
            // 用户取消删除
            this.$message.info('已取消删除');
          });
        },
        
        // 开始编辑会话标题
        startEditSession(sessionId, category, index, content) {
          // 设置编辑状态
          this.editingSessionId = sessionId;
          this.editingCategory = category;
          this.editingIndex = index;
          this.editingTitle = content;
          
          // 等待DOM更新后聚焦输入框
          this.$nextTick(() => {
            const input = this.$refs.editInput;
            if (input && input.length > 0) {
              input[input.length - 1].focus();
              // 选中所有文本
              input[input.length - 1].select();
            }
          });
        },
        
        // 保存编辑的会话标题
        async saveEditSession() {
          // 检查标题是否为空
          const trimmedTitle = this.editingTitle.trim();
          if (!trimmedTitle) {
            this.$message.warning('标题不能为空');
            // 重新聚焦输入框
            this.$nextTick(() => {
              const input = this.$refs.editInput;
              if (input && input.length > 0) {
                input[input.length - 1].focus();
              }
            });
            return;
          }
          
          try {
            // 显示加载状态
            this.loading = true;
            
            // 调用API更新会话标题
            // 注意：这里的API名称需要根据实际情况调整
            const { errCode, msg } = await this.$api.ChatSessionEditTitle({
              chatId: this.editingSessionId,
              title: trimmedTitle
            });
            
            if (errCode == 0) {
              // 更新本地数据
              let targetArray;
              switch(this.editingCategory) {
                case 'today':
                  targetArray = this.todaySessions;
                  break;
                case 'recent':
                  targetArray = this.recentSessions;
                  break;
                case 'earlier':
                  targetArray = this.earlierSessions;
                  break;
              }
              
              if (targetArray && targetArray[this.editingIndex]) {
                targetArray[this.editingIndex].content = trimmedTitle;
              }
              
              this.$message.success('标题修改成功');
            } else {
              this.$message.error(msg || '标题修改失败');
            }
          } catch (error) {
            console.error('保存会话标题失败:', error);
            this.$message.error('保存失败，请重试');
          } finally {
            // 隐藏加载状态并重置编辑状态
            this.loading = false;
            this.resetEditState();
          }
        },
        
        // 取消编辑
        cancelEditSession() {
          this.resetEditState();
        },
        
        // 重置编辑状态
        resetEditState() {
          this.editingSessionId = null;
          this.editingCategory = null;
          this.editingIndex = null;
          this.editingTitle = '';
        },
        
      scrollTop() {
        this.$nextTick(() => {
          this.timer = setInterval(() => {
            const box = this.$refs.chattingContent
            if(box){
              box.scrollTop = box.scrollHeight
            }
          }, 1300)
        })
      }
    }
}
</script>

<style lang="scss" scoped>
/* 深度思考内容样式 */
.thinking-content {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 5px;
  background-color: #f9f9f9;
  border-left: 3px solid #eee;
}
.thinking-content .thinking-markdown {
  color: #ccc;
  font-size: 14px;
}

.Ai-page{
    height: 100%;
    background-color: #F5F7FA;
    .Ai-content{
      border-top: 1px solid transparent;
      height: calc(100% - 50px);
      background: url('../../assets/public/AI-bg.png') no-repeat center/cover;
      position: relative;
    }

    .ai-tip-box{
      width: 800px;
      margin: 40px auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .tip-label{
        margin-left:6px;
        height: 70px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        p,
        h4{
          margin: 0;
        }
        h4{
          font-size: 18px;
          color: #333;
        }
        p{
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 13px;
          color: #666666;
          line-height: 18px;
          text-align: justify;
          font-style: normal;
          margin-top: 8px;
          span{
            margin-left: 14px;
            color: #0070FC;
          }
        }
      }
    }

    .ai-default-tip{
      width: 800px;
      margin: 0 auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      
      ::v-deep .el-card__header{
        padding: 13px 2px;
        border: none;
      }

      ::v-deep .el-card__body{
        height: 234px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 4px;
        padding:10px;
      }
     
      .card-1{
        width: 430px;
        background-color: #B9E9FE;
        .clearfix1{
          p{
            color: #023247;
            font-size: 15px;
            span{
              color: #666666;
              font-size: 13px;
            }
          }
        }

        .item-tip{
          height: 53px;
          line-height: 53px;
          border-bottom: 1px solid #D3EAF4;
          font-size: 14px;
          position: relative;
          padding-left: 26px;
          &:before{
            content: '';
            position: absolute;
            left: 10px;
            top: 24px;
            width: 5px;
            height: 5px;
            border-radius: 5px;
            background: #666666;
          }
        }
       
      }
      .card-2{
        width: 350px;
        background-color: #E5DBFE;
        .clearfix2{
          p{
            color: #4720A8;
            font-size: 15px;
            span{
              color: #666666;
              font-size: 13px;
            }
          }
        }

        .card-item{
          display: flex;
          justify-content: flex-start;
          align-items: center;
          height:45px;
          border-bottom: 1px solid #ECE5FF;

          .item-icon{
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: #D4E0FF;
            color: #2B66FF;
            text-align: center;
            line-height: 36px;
            font-size: 20px;
            margin-right: 10px;
          }
          .right-content{
            display:flex;
            justify-content: space-around;
            flex-direction: column;
            align-items: flex-start;
          }
          .up-tip{
            height: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: justify;
            font-style: normal;
          }
          .sub-tip{
            height: 18px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #666666;
            line-height: 18px;
            text-align: justify;
            font-style: normal;
          }
        }
        

      }
      .box-card{
        height: 300px;
        border-radius: 4px;
        padding: 10px;
        box-sizing: border-box;
        margin-right: 2%;
        &:last-child{
          margin-right: 0;
        }
      }
    }

    .chat-box{
      width: 800px;
      height: 120px;
      background: #FFFFFF;
      box-shadow: 1px 1px 10px 0px rgba(153,153,153,0.1);
      border-radius: 10px;
      margin: 110px auto 20px;
      padding: 15px 15px 12px 15px;
      box-sizing: border-box;
      .chat-input{
        ::v-deep .el-textarea__inner{
          height: 64px;
          border: none;
          resize: none;
        }
      }
      .chat-btn-box{
        display: flex;
        justify-content: space-between;
        align-items: center;

        .deep-think-btn{
          width: 100px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 8px;
          color:#333;
          padding:0;
          transition: all 0.3s ease;

        }
        .deep-think-btn.thinking {
          background: #0070FC;
          color: #fff;
        }
        .send-btn{
          width: 36px;
          height: 36px;
          background: linear-gradient( 90deg, #0070FC 0%, #E38EFC 100%);
          border-radius: 18px;
          color:#fff;
          padding:0;
        }
      }
    }
}
    /* 聊天内容区域样式 */
    #chattingContent {
      width: 800px;
      height: 400px;
      overflow-y: auto;
      margin: 20px auto;
      padding: 20px;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    }
    
    /* 消息日期样式 */
    .msg-date {
      text-align: center;
      color: #999;
      font-size: 12px;
      margin-bottom: 10px;
    }
    
    /* 消息容器样式 */
    .chatting-item {
      margin-bottom: 20px;
    }
    
    .chatting-item .msg-from {
      display: flex;
      align-items: flex-start;
    }
    
    /* 用户头像圆形容器样式 */
    .avatar-container {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #0070FC;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-right: 10px;
      flex-shrink: 0;
    }
    
    /* 消息内容样式 */
    .msg-content {
      max-width: 70%;
      padding: 10px 15px;
      border-radius: 10px;
      word-wrap: break-word;
      line-height: 1.5;
    }
    
    /* 用户消息样式 - 右边 */
    .chatting-item.self .msg-from {
      justify-content: flex-end;
    }
    
    .chatting-item.self .msg-content {
      background-color: #0070FC;
      color: white;
      margin-right: 10px;
      border-bottom-right-radius: 4px;
    }
    
    .chatting-item.self .avatar-container {
      margin-left: 10px;
      margin-right: 0;
    }
    
    /* AI消息样式 - 左边 */
    .chatting-item.other .msg-from {
      justify-content: flex-start;
    }
    
    .chatting-item.other img {
      margin-right: 10px;
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    
    .chatting-item.other .msg-content {
      background-color: #FFFFFF;
      color: #333;
      margin-left: 10px;
      border-bottom-left-radius: 4px;
    }
    
    /* 加载状态样式 */
    .disloading {
      display: flex;
      align-items: center;
    }
    
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #0070FC;
      margin-right: 5px;
      animation: loading 1.4s ease-in-out infinite;
    }
    
    .dot:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    .dot:nth-child(3) {
      animation-delay: 0.4s;
    }
    
    @keyframes loading {
      0%, 60%, 100% {
        transform: scale(0.8);
        opacity: 0.3;
      }
      30% {
        transform: scale(1);
        opacity: 1;
      }
    }
    
    /* 确保聊天框位置调整 */
    .chat-box {
      margin-top: 20px;
    }
  
    /* 历史会话容器样式 */
    .history-container {
      position: absolute;
      top: 0;
      left: -300px; /* 默认在屏幕左侧外 */
      width: 300px;
      height: calc(100vh - 110px);
      background: white;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
      z-index: 100; /* 确保在Ai-content上层 */
      transition: left 0.3s ease; /* 从左到右的动画效果 */
      display: flex;
      flex-direction: column;
    }
    
    /* 显示历史会话容器 */
    .history-container.show {
      left: 0;
    }
    
    /* 历史会话容器头部 */
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #E4E7ED;
      background-color: #FAFAFA;
    }
    
    .history-header h3 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }
    
    /* 历史会话容器内的关闭按钮 */
    .history-header .history-toggle-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #909399;
      border-radius: 4px;
      transition: all 0.3s ease;
    }
    
    .history-header .history-toggle-btn:hover {
      background-color: #F0F0F0;
    }
    
    /* 历史会话列表区域 */
    .history-list {
      flex: 1;
      overflow-y: auto;
      padding: 10px 0;
      /* 自定义滚动条样式 */
    }
    
    /* 自定义滚动条 - Webkit浏览器 */
    .history-list::-webkit-scrollbar {
      width: 6px;
    }
    
    .history-list::-webkit-scrollbar-track {
      background: #F5F7FA;
    }
    
    .history-list::-webkit-scrollbar-thumb {
      background: #C0C4CC;
      border-radius: 3px;
      transition: background 0.3s ease;
    }
    
    .history-list::-webkit-scrollbar-thumb:hover {
      background: #909399;
    }
    
    /* 自定义滚动条 - Firefox浏览器 */
    .history-list {
      scrollbar-width: thin;
      scrollbar-color: #C0C4CC #F5F7FA;
    }
    
    /* 历史会话分组标题 */
    .history-group {
      margin-bottom: 20px;
    }
    
    .history-group-title {
      padding: 0 20px;
      margin-bottom: 10px;
      font-size: 12px;
      color: #909399;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    /* 历史会话项 */
    .history-item {
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }
    
    .history-item:hover {
      background-color: #F5F7FA;
      transform: translateX(2px);
    }
    
    /* 历史会话删除按钮 */
    .history-delete-btn {
      position: absolute;
      right: 40px;
      top: 50%;
      transform: translateY(-50%);
      color: #C0C4CC;
      font-size: 16px;
      cursor: pointer;
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .history-item:hover .history-delete-btn {
      opacity: 1;
    }
    
    .history-delete-btn:hover {
      color: #F56C6C;
    }
    
    /* 历史会话编辑按钮 */
    .history-edit-btn {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #C0C4CC;
      font-size: 16px;
      cursor: pointer;
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .history-item:hover .history-edit-btn {
      opacity: 1;
    }
    
    .history-edit-btn:hover {
      color: #409EFF;
    }
    
    /* 编辑输入框样式 */
    .history-item-edit {
      width: calc(100% - 80px);
    }
    
    .edit-input {
      width: 100%;
      padding: 4px 8px;
      border: 1px solid #409EFF;
      border-radius: 4px;
      font-size: 14px;
      outline: none;
      box-sizing: border-box;
    }
    
    .edit-input:focus {
      border-color: #66b1ff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
    
    /* 历史会话项激活状态 */
    .history-item.active {
      background-color: #E8F3FF;
      border-left: 3px solid #0070FC;
    }
    
    .history-item.active .history-item-content {
      color: #0070FC;
      font-weight: 500;
    }
    
    .history-item-content {
      font-size: 14px;
      color: #333;
      margin-bottom: 4px;
      line-height: 1.5;
      word-break: break-word;
    }
    
    .history-item-time {
      font-size: 12px;
      color: #909399;
    }
    
    /* 历史会话容器底部 */
    .history-footer {
      padding: 15px 20px;
      border-top: 1px solid #E4E7ED;
      text-align: center;
      background-color: #FAFAFA;
    }
    
    .history-tip {
      font-size: 12px;
      color: #909399;
    }
    
    /* 主内容区域的展开按钮 */
    .history-toggle-btn-main {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 80px;
      height: 32px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #333;
      font-size: 12px;
      transition: all 0.3s ease;
      z-index: 10;
    }
    
    .history-toggle-btn-main:hover {
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  </style>