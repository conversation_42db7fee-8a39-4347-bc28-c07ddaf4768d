<template>
  <div class="tinymce-editor">
    <editor
      v-model="myValue"
      :init="init"
      api-key="ei07420ih40dy3urd9s8syytj3qp9cufzz3adpnppivqv16k"
      :disabled="disabled"
    />
    <!-- <VueTinymce ref="VueTinymceTableRef" v-model="myValue" :setting="settings" placeholder="请输入内容" /> -->
  </div>
</template>

<script>
import { settings } from '@/utils/tinymceSettings.js'
import Editor from '@tinymce/tinymce-vue'
// 常量
import config from './util/glob'

import ImageMixin from './tools/Image/ImageMixin.js'
// 引入视频功能
import VideoMixin from './tools/Video/VideoMixin.js'
// 插入重点词功能
import NoteMixin from './tools/Note/NoteMixin.js'
// 引入轮播图功能
import CarouselMixin from './tools/Carousel/CarouselMixin.js'
// 引入折叠面板功能
import FoldPanelMixin from './tools/FoldPanel/FoldPanelMixin.js'
// 引入H5卡片功能
import IframeMixin from './tools/Iframe/IframeMixin.js'
// 引入参考文献功能
import LiteratureMixin from './tools/Literature/LiteratureMixin.js'

// 动态引入轮播图的css
import carouselCss from '!!raw-loader!@/components/Tinymce/tools/Carousel/Carousel.css'
// 动态引入折叠面板的css
import foldPanelCss from '!!raw-loader!@/components/Tinymce/tools/FoldPanel/FoldPanel.css'
// 动态引入参考文献的css
import literatureCss from '!!raw-loader!@/components/Tinymce/tools/Literature/Literature.css'
// 动态引入重点词的css
import noteCss from '!!raw-loader!@/components/Tinymce/tools/Note/Note.css'
// 动态引入iframe的css
import iframeCss from '!!raw-loader!@/components/Tinymce/tools/Iframe/Iframe.css'

export default {
  name: 'TinymceEditor',
  components: {
    editor: Editor
  },
  mixins: [NoteMixin, CarouselMixin, FoldPanelMixin, IframeMixin, LiteratureMixin, VideoMixin, ImageMixin],
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: Boolean,
    nowSection: {
      type: Object,
      default: () => ({})
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    quickbars_insert_toolbar: {
      type: String,
      default: ''
    }
  },
  data(vm) {
    return {
      myValue: this.value,
      editor: null,
      init: {
        height: '100%',
        menubar: false,
        branding: true,
        statusbar: false,
        language: 'zh_CN',
        readonly: false, // 设置只读属性 默认 false
        draggable_modal: true, // 模态框拖动 默认false
        placeholder: '', // 占位符
        plugins: config.Plugins,
        toolbar: vm.showToolbar ? config.Toolbar : false,
        quickbars_selection_toolbar: config.quickbars_selection_toolbar + vm.quickbars_insert_toolbar, // 设置 快速选择 触发提供的工具栏 需引入插件  默认 'alignleft aligncenter alignright' 设置为false禁用
        // quickbars_insert_toolbar: config.quickbars_insert_toolbar, // 设置 快速插入 触发提供的工具栏 需引入插件quickbars 默认 quickimage quicktable 设置为false禁用
        quickbars_insert_toolbar: '', // 设置 快速插入 触发提供的工具栏 需引入插件quickbars 默认 quickimage quicktable 设置为false禁用
        font_formats: config.font_formats,
        fontsize_formats: config.fontsize_formats,
        skin: 'oxide',
        content_css: [],
        content_style: carouselCss + foldPanelCss + literatureCss + noteCss + iframeCss + ' body.mce-content-body { padding: 40px 72px !important; } .mce-content-body:not([dir=rtl])[data-mce-placeholder]:not(.mce-visualblocks)::before{left:72px;top:40px;} p {margin: 0; padding: 0; margin-block-start: 0;margin-block-end: 0;}',
        paste_data_images: true,
        setup: (editor) => {
          console.log('富文本初始化--editor', editor, ',this.nowSection', this.nowSection)
          config.setCuIcon(editor, this)
          this.editor = editor
          // 自定义工具栏布局
          editor.on('init', () => {
            editor.focus()
            setTimeout(() => {
              try {
                editor.focus()
                editor.execCommand('FontName', false, 'STSong,serif')
                editor.execCommand('FontSize', false, '14pt')
              } catch (error) {
              }
            }, 500) // 延迟执行，确保 selection 存在

            // this.initVideoFeature()
            // this.initImageFeature()
            this.registerNoteButtons()
            this.registerNotesButton() // 笔记按钮
            this.initNoteFeature()
            // this.initCarouselFeature()
            this.initFoldPanelFeature()
            this.initIframeFeature()
            this.initLiteratureFeature()
          })
          editor.on('SetContent', (e) => {
            console.log('内容已插入编辑器:')
            editor.focus()
            this.initExistingImage()
            this.initExistingVideo()
            this.initExistingCarousels()
          })
        },
      }
    }
  },
  watch: {
    value(val) {
      // console.log('监听富文本value',val);
      this.myValue = val
      if (this.editor && val) {
        console.log('富文本内容变化---监听')
        this.updateNoteMarks()
        // this.initCarouselFeature()
        this.initFoldPanelFeature()
        this.initIframeFeature()
        this.initLiteratureFeature()
        // this.initVideoFeature()
        // this.initExistingImage()
      }
    },
    nowSection(nv, ov) {
      // if(nv && nv != ov) {
      //   console.log('初始化', this.editor,);
      //   if(!this.editor) return
      //      this.initNoteFeature(this.editor)
      // }
    },
    myValue(val) {
      this.$emit('input', val)
    }
  },
  mounted() {},
  methods: {
    clear() {
      this.myValue = ''
    },
    getContent() {
      return this.editor.getContent()
    },
    getContentHtml() {
      return this.editor.getBody()
    },
    getContentText() {
      return this.editor.getContent({ format: 'text' })
    },
  }
}
</script>
<style scoped>
.tinymce-editor {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss" scoped>
::v-deep .tox-sidebar-wrap {
    width: 840px !important;
    margin: 0 auto !important;
    margin-top: 10px !important;
  }
  .tox-tinymce {
    border: 0 !important;
  }
::v-deep .tox-editor-header {
    .tox-toolbar__primary {
        background: transparent !important;
        .tox-toolbar__group {
            padding: 6px 14px;
        }
        &>.tox-toolbar__group:nth-of-type(1) {
            /* 使用flex布局，设置为列方向实现上下两行 */
            display: flex !important;
            flex-wrap: wrap !important;
            align-items: center !important;
            justify-content: center !important;
            width: 110px !important;
            // .tox-tbtn__select-label {
            //     display: none;
            // }
            .tox-tbtn {
                height: auto !important;
                width: 40px !important;
            }
        }
        &>.tox-toolbar__group:nth-of-type(2) {
            display: flex !important;
            flex-wrap: wrap !important;
            align-items: center !important;
            justify-content: space-between !important;
            width: 334px !important;
            >.tox-tbtn:nth-of-type(1),
            >.tox-tbtn:nth-of-type(2),
            >.tox-tbtn:nth-of-type(3) {
                border: 1px solid #E7E7E7 !important;
                width: 92px !important;
            }
            .tox-split-button {
                border: 0 !important;
            }
            // .tox-tbtn__select-label {
            //     display: none;
            // }
        }
         &>.tox-toolbar__group:nth-of-type(3) {
            display: flex !important;
            flex-wrap: wrap !important;
            align-items: center !important;
            justify-content: space-between !important;
            width: 230px !important;
            .tox-tbtn__select-label {
                display: none;
            }
            .tox-tbtn {
              width: 37px;
            }
        }
        &>.tox-toolbar__group:nth-of-type(4) {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            .tox-tbtn {
                margin-top: 10px!important;
                display: flex !important;
                align-items: center !important;
                justify-content: space-between !important;
                 flex-direction: column;
                height: auto !important;
                width: auto !important;
            }

        }

    }
}
p {
  margin: 0;
  padding: 0;
  margin-block-start: auto;
  margin-block-end: auto;
}
</style>
