<template>
  <base-dialog
    :visible.sync="dialogVisible"
    :title="title"
    width="600px"
    class="note-add-dialog !z-1000"
  >
    <div class="h-400px w-full flex flex-col">
      <Editor v-model="content"  :init="init"/>
    </div>
    <template #footer>
      <div class="w-full flex justify-end pt-12px">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="close">确定</el-button>
      </div>
    </template>
  </base-dialog>
</template>

<script>
import BaseDialog from '@/components/base/dialog.vue'
import Editor from '@tinymce/tinymce-vue'
import config from "@/components/Tinymce/util/glob";
export default {
  name: 'NotesDialog',
  components: {
    BaseDialog,
    Editor
  },
  props: {},
  data() {
    return {
      title: '笔记',
      content: '',
      dialogVisible: false,
      init: {
        placeholder: '请输入笔记内容',
        height: '100%',
        menubar: false,
        branding: true,
        statusbar: false,
        language: 'zh_CN',
        toolbar: 'forecolor bold fontselect fontsizeselect',
        skin: 'oxide',
        font_formats: config.font_formats,
        fontsize_formats: config.fontsize_formats,
      }
    }
  },
  methods: {
    open({ title }) {
      this.title = title
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__wrapper {
  z-index: 1000 !important;
}
</style>
