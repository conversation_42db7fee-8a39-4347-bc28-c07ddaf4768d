<template>
    <div class="exam-list-page">
        <div class="top-tool">
            <div class="left">
                <el-input class="serach-input" clearable @change="initList()" v-model="searchParams.Name" placeholder="请输入任务名称" suffix-icon="iconfont icon-sousuo"></el-input>
                <el-select class="select-status" clearable @change="initList()" v-model="searchParams.Status" placeholder="请选择作业状态">
                    <el-option v-for="item in statusList" :key="item.id" :label="item.name" :value="item.value"></el-option>
                </el-select>
                <el-select class="select-status" clearable @change="initList()" v-model="searchParams.TagType" placeholder="请选择作业类型">
                    <el-option v-for="item in taskTypeList" :key="item.id" :label="item.name" :value="item.value"></el-option>
                </el-select>

            </div>
            <el-button class="add-task-btn" @click="addTask" type="primary"><i class="iconfont icon-xinjian"></i> 创建作业</el-button>
        </div>
        <div class="task-list">
             <!-- 使用 Element UI 栅格系统 -->
             <el-row :gutter="10" v-if="taskList.length>0">
                <el-col :span="24" v-for="item in taskList" :key="item.id">
                    <div class="task-item">
                        <p :class="getTaskStatusClass(item.status)">{{getTaskStatusText(item.status)}}</p>
                        <h3 class="task-title">{{item.name}} <span :class="getTagTypeClass(item.tagType)">{{item.tagType}}</span></h3>
                        <p class="chapter-info">所属章节：课程设计 > 课程标准</p>
                        <div class="task-info">
                            <el-row class="left-info">
                                <el-col :span="12" class="info-item">
                                    <p class="lab">开始时间</p>  &nbsp;:&nbsp;
                                    <p class="val">{{formatISOString(item.beginTime)}}</p>
                                </el-col>
                                <el-col :span="12" class="info-item">
                                    <p class="lab">结束时间</p>  &nbsp;:&nbsp;
                                    <p class="val">{{formatISOString(item.endTime)}}</p>

                                </el-col>
                                <el-col :span="12" class="info-item">
                                    <p class="lab">题量</p>  &nbsp;:&nbsp;
                                    <p class="val">{{item.questionCount}}</p>
                                </el-col>
                                <el-col :span="12" class="info-item">
                                    <p class="lab">卷面分</p>  &nbsp;:&nbsp;
                                    <p class="val">{{item.totalScore}}</p>
                                </el-col>
                                <el-col :span="12" class="info-item">

                                    <p class="lab">已交/未交</p>  &nbsp;:&nbsp;
                                    <p class="val">{{item.submittedCount}}<span class="lab">/ {{item.unsubmittedCount}}</span></p>
                                </el-col>
                                <el-col :span="12" class="info-item">
                                    <p class="lab">待批阅</p>  &nbsp;:&nbsp;
                                    <p class="val">{{item.pendingReviewCount}}</p>
                                </el-col>
                            </el-row>
                            <div class="operate-box">
                                <el-button class="edit-btn" @click="edit(item)">修改任务</el-button>
                                <el-button class="remark-btn" @click="review">批阅</el-button>
                                <el-button class="static-btn" @click="statistics(item)">详情统计</el-button>
                                <el-button class="del-btn" @click="del(item)">删除</el-button>
                            </div>
                        </div>
                        <p class="task-time">
                            <span class="remark">备注：{{item.description}}</span>
                        </p>
                       
                    </div>
                </el-col>
            </el-row>
            <empty style="margin-top:100px;"  v-if="taskList.length==0" msg="暂无数据" size="middle" />
        </div>
    </div>
</template>
  
<script>
    import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
    export default {
        name: 'examTaskList',
        data() {
            return {
                taskName: '',
                taskStatus: '',
                statusList:[
                    {id:1,name:'未开始',value:'NotStarted'},
                    {id:2,name:'进行中',value:'InProgress'},
                    {id:3,name:'已完成',value:'Finished'}
                ],
                taskTypeList:[
                    {id:1,name:'课前预习',value:'课前预习'},
                    {id:2,name:'课中授课',value:'课中授课'},
                    {id:3,name:'课后巩固',value:'课后巩固'}
                ],
                taskList: [], // 任务列表
                searchParams: { // 搜索参数
                    CourseId: this.$route.query.courseId, // 任务名称
                    Name: '', // 任务名称
                    Status: '', // 任务状态
                    TaskCategory:'作业', // 任务类型 作业||考试
                    TagType:'', // 考试类型 课前预习, 课中授课, 课后巩固
                    PageIndex: 1, // 页码
                    PageSize: 10 // 每页数量
                }
            } 
        },
        components: {
            // 引入组件
            empty:()=>import("@/components/base/empty.vue"),
        },
        mounted() {
            this.initList(); // 初始化任务列表
        },
        methods: {
            formatISOString,
            // 根据任务类型返回对应的CSS类名
            getTagTypeClass(tagType) {
                switch(tagType) {
                    case '课前预习':
                        return 'label-1';
                    case '课中授课':
                        return 'label-2';
                    case '课后巩固':
                        return 'label-3';
                    default:
                        return '';
                }
            },
            // 获取任务状态对应的CSS类名
            getTaskStatusClass(status) {
                const baseClass = 'task-label';
                let statusClass = '';
                switch(status) {
                    case 1:
                        statusClass = 'status1';
                        break;
                    case 2:
                        statusClass = 'status2';
                        break;
                    case 3:
                        statusClass = 'status3';
                        break;
                }
                return [baseClass, statusClass].join(' ');
            },
            // 获取任务状态对应的文本
            getTaskStatusText(status) {
                switch(status) {
                    case 'NotStarted':
                        return '未开始';
                    case 'InProgress':
                        return '进行中';
                    case 'Finished':
                        return '已完成';
                    default:
                        return '';
                }
            },
            initList() {
                // 初始化任务列表
                this.$api.TaskGetList(this.searchParams).then(res => {
                    this.taskList = res.data.items; // 假设接口返回的数据是一个数组 
                })
            },
            handleClick(tab, event) {
                console.log(tab, event);
            },
            addTask() {
                this.$emit('handleEvent','singleTask',{taskCategory:'作业'});
            },
            edit(item) {
                this.$emit('handleEvent','singleTask',item);
            },
            review() {
                this.$emit('handleEvent','taskReview');
            },
            statistics(item) {
                this.$emit('handleEvent','taskStatistics',item);
            },
            del(item) {
                this.$zdDialog({
                    width:'400px',
                    center:true,
                    contTitle: '确定要删除该任务吗？?',
                }).then(async()=>{
                    this.$api.DeleteTask({id:item.id}).then(res=>{ // 删除任务
                        this.$message.success('删除成功'); // 提示删除成功
                        this.initList(); // 重新加载任务列表
                    })
                })
            }
        }
    }
</script>   
  
<style lang="scss" scoped>
.exam-list-page {
    height: 100%;
    .top-tool{
       display: flex;
       justify-content: space-between;
       align-items: center;
       padding: 10px 0 10px 0;
       background-color: #F6F8FA;

       .left{
           display: flex;
           align-items: center;
           justify-content: flex-start;
            .serach-input{
                width: 220px;
                margin-right: 10px;
            }
            .select-status{
                width: 220px;
                margin-right: 10px;
            }
       }
       .add-task-btn{
            width: 120px;
            height: 38px;
            padding: 0;
            background: #07C392;
            color: #fff;
            border-radius: 4px;
            border: none;
            &:hover{
                background: #10D3A0;
            }
       }
    }

    .task-list{
       background-color: #F6F8FA;

       .task-item{
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            position: relative;
            margin-bottom: 10px;
            overflow: hidden;
            padding-bottom: 14px;
            .task-label{
                position: absolute;
                right: -30px;
                top:22px;
                width: 120px;
                background-color: #07C392;
                transform: rotate(45deg);
                height: 26px;
                line-height: 26px;
                font-size: 14px;
                color: #fff;
                text-align: center;
            }
            .status1{
                background-color: #BBBBBB;
            }
            .status2{
                background-color: #0070FC;
            }
            .status3{
                background-color: #07C392;
            }
           .task-title{
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                margin: 0;
                line-height: 20px;
                margin-bottom:6px;
                .label-1{
                    width: 70px;
                    height: 20px;
                    padding: 0 6px;
                    margin-left:10px;
                    background: #EDF2FF;
                    border-radius: 4px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 13px;
                    color: #0070FC;
                    line-height: 18px;
                    text-align: left;
                    font-style: normal;
                }
                .label-2{
                    width: 70px;
                    height: 20px;
                    padding: 0 6px;
                    margin-left:10px;
                    background: #FFF7EA;
                    border-radius: 4px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 13px;
                    color: #E08E13;
                    line-height: 18px;
                    text-align: left;
                    font-style: normal;
                }
                .label-3{
                    width: 70px;
                    height: 20px;
                    padding: 0 6px;
                    margin-left:10px;
                    background: #E5FCF5;
                    border-radius: 4px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 13px;
                    color: #07C392;
                    line-height: 18px;
                    text-align: left;
                    font-style: normal;
                }



           }
           .chapter-info{
                height: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 13px;
                color: #999999;
                line-height: 18px;
                text-align: justify;
                font-style: normal;
           }
          .task-info{
            /* height: 80px; */
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* background: #F6FAFF; */
            background: #FFF;
            /* border-radius: 4px; */
            margin-top:18px;

            .left-info{
                width: 520px;
            }

            .info-item{
                text-align: center;
                font-size: 14px;
                display: flex;
                justify-content: flex-start;
                margin-bottom: 10px;
                
                .lab,
                .val{
                    height: 20px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #5C6075;
                    line-height: 20px;
                    text-align: justify;
                    font-style: normal;
                }

                .lab{
                    width: 66px;
                    text-align: justify;
                    text-align-last: justify;
                }
            }
          }

         .task-time{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 24px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            border-top: 1px solid #F2F3F5;
            .remark{
                padding-top:10px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;
            }
           
         }

        .operate-box{
            text-align: right;
            .edit-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #0070FC;
                color: #0070FC;
            } 
            .remark-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            } 
            .static-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            }
            .del-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            } 
         }
        }
    }
}
</style>