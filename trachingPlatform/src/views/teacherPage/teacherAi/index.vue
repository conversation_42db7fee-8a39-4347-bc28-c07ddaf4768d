<template>
  <div class="teacher-Ai-page">
    <div class="top">
      <span class="back-icon" @click="goPath()">
        <i class="iconfont icon-fanhui"></i> 
      </span>
      <el-breadcrumb>
          <el-breadcrumb-item :to="{ path:'/teacher/teacherHome'}">工作台</el-breadcrumb-item>
          <el-breadcrumb-item> AI 助教</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <!-- 主内容区域 -->
    <AICom :type="'teacher'" />
  </div>
</template>

<script>
import AICom from "@/components/ai-dialog/AI-chat.vue"  

export default {
    data() {
        return {
          isDeepThinking: false,
          isInput:false,
          loading:false,// 对话loading
          messageList:[],// 对话记录列表
          chatId:0,
          chatuniqueId: Date.now().toString(),
          inputValue:'',
          abortController:null,
          markdownRender:md,
          markDownText:'',
          showHistoryPanel: false, // 历史会话面板显示状态
          
          // 模拟历史会话数据 - 用于展示效果
          todaySessions: [
            
          ],
          recentSessions: [
            
          ],
          earlierSessions: [
            
          ],
          // 编辑相关状态
          editingSessionId: null,
          editingCategory: null,
          editingIndex: null,
          editingTitle: ''
        }
    },
    components:{
      AICom
    },
    mounted(){

    },
    methods: {
      goPath(){
        this.$router.push({
          path: '/teacher/teacherCourse'
        })
      }
    }
}
</script>

<style lang="scss" scoped>
.teacher-Ai-page{
    height: 100%;
    background-color: #F5F7FA;
    .top{
      height: 50px;
      background: #FFFFFF;
      display: flex;
      padding: 0 30px;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      .back-icon{
          width: 20px;
          height: 20px;
          background: #BBBBBB;
          border-radius: 4px;
          color: #fff;
          line-height: 20px;
          text-align: center;
          display: inline-block;
          margin-right: 20px;
          cursor: pointer;
          .iconfont{
              font-size: 12px;
              line-height: 20px;
          }
      }
      ::v-deep .el-breadcrumb__inner a, 
      ::v-deep.el-breadcrumb__inner.is-link{
          color: #bbb;
      }
      
      ::v-deep .el-breadcrumb__item:last-child .el-breadcrumb__inner{
          color: #0070FC;
      }
    }

    .Ai-content{
      border-top: 1px solid transparent;
      height: calc(100% - 50px);
      background: url('../../../assets/public/AI-bg.png') no-repeat center/cover;
      position: relative;
    }

    .ai-tip-box{
      width: 800px;
      margin: 40px auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .tip-label{
        margin-left:6px;
        height: 70px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        p,
        h4{
          margin: 0;
        }
        h4{
          font-size: 18px;
          color: #333;
        }
        p{
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 13px;
          color: #666666;
          line-height: 18px;
          text-align: justify;
          font-style: normal;
          margin-top: 8px;
          span{
            margin-left: 14px;
            color: #0070FC;
          }
        }
      }
    }

    .ai-default-tip{
      width: 800px;
      margin: 0 auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      
      ::v-deep .el-card__header{
        padding: 13px 2px;
        border: none;
      }

      ::v-deep .el-card__body{
        height: 234px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 4px;
        padding:10px;
      }
     
      .card-1{
        width: 430px;
        background-color: #B9E9FE;
        .clearfix1{
          p{
            color: #023247;
            font-size: 15px;
            span{
              color: #666666;
              font-size: 13px;
            }
          }
        }

        .item-tip{
          height: 53px;
          line-height: 53px;
          border-bottom: 1px solid #D3EAF4;
          font-size: 14px;
          position: relative;
          padding-left: 26px;
          &:before{
            content: '';
            position: absolute;
            left: 10px;
            top: 24px;
            width: 5px;
            height: 5px;
            border-radius: 5px;
            background: #666666;
          }
        }
       
      }
      .card-2{
        width: 350px;
        background-color: #E5DBFE;
        .clearfix2{
          p{
            color: #4720A8;
            font-size: 15px;
            span{
              color: #666666;
              font-size: 13px;
            }
          }
        }

        .card-item{
          display: flex;
          justify-content: flex-start;
          align-items: center;
          height:45px;
          border-bottom: 1px solid #ECE5FF;

          .item-icon{
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: #D4E0FF;
            color: #2B66FF;
            text-align: center;
            line-height: 36px;
            font-size: 20px;
            margin-right: 10px;
          }
          .right-content{
            display:flex;
            justify-content: space-around;
            flex-direction: column;
            align-items: flex-start;
          }
          .up-tip{
            height: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: justify;
            font-style: normal;
          }
          .sub-tip{
            height: 18px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #666666;
            line-height: 18px;
            text-align: justify;
            font-style: normal;
          }
        }
        

      }
      .box-card{
        height: 300px;
        border-radius: 4px;
        padding: 10px;
        box-sizing: border-box;
        margin-right: 2%;
        &:last-child{
          margin-right: 0;
        }
      }
    }

    .chat-box{
      width: 800px;
      height: 120px;
      background: #FFFFFF;
      box-shadow: 1px 1px 10px 0px rgba(153,153,153,0.1);
      border-radius: 10px;
      margin: 110px auto 20px;
      padding: 15px 15px 12px 15px;
      box-sizing: border-box;
      .chat-input{
        ::v-deep .el-textarea__inner{
          height: 64px;
          border: none;
          resize: none;
        }
      }
      .chat-btn-box{
        display: flex;
        justify-content: space-between;
        align-items: center;

        .deep-think-btn{
          width: 100px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 8px;
          color:#333;
          padding:0;
          transition: all 0.3s ease;

        }
        .deep-think-btn.thinking {
          background: #0070FC;
          color: #fff;
        }
        .send-btn{
          width: 36px;
          height: 36px;
          background: linear-gradient( 90deg, #0070FC 0%, #E38EFC 100%);
          border-radius: 18px;
          color:#fff;
          padding:0;
        }
      }
    }
}
    /* 聊天内容区域样式 */
    #chattingContent {
      width: 800px;
      height: 400px;
      overflow-y: auto;
      margin: 20px auto;
      padding: 20px;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    }
    
    /* 消息日期样式 */
    .msg-date {
      text-align: center;
      color: #999;
      font-size: 12px;
      margin-bottom: 10px;
    }
    
    /* 消息容器样式 */
    .chatting-item {
      margin-bottom: 20px;
    }
    
    .chatting-item .msg-from {
      display: flex;
      align-items: flex-start;
    }
    
    /* 用户头像圆形容器样式 */
    .avatar-container {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #0070FC;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-right: 10px;
      flex-shrink: 0;
    }
    
    /* 消息内容样式 */
    .msg-content {
      max-width: 70%;
      padding: 10px 15px;
      border-radius: 10px;
      word-wrap: break-word;
      line-height: 1.5;
    }
    
    /* 用户消息样式 - 右边 */
    .chatting-item.self .msg-from {
      justify-content: flex-end;
    }
    
    .chatting-item.self .msg-content {
      background-color: #0070FC;
      color: white;
      margin-right: 10px;
      border-bottom-right-radius: 4px;
    }
    
    .chatting-item.self .avatar-container {
      margin-left: 10px;
      margin-right: 0;
    }
    
    /* AI消息样式 - 左边 */
    .chatting-item.other .msg-from {
      justify-content: flex-start;
    }
    
    .chatting-item.other img {
      margin-right: 10px;
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    
    .chatting-item.other .msg-content {
      background-color: #FFFFFF;
      color: #333;
      margin-left: 10px;
      border-bottom-left-radius: 4px;
    }
    
    /* 加载状态样式 */
    .disloading {
      display: flex;
      align-items: center;
    }
    
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #0070FC;
      margin-right: 5px;
      animation: loading 1.4s ease-in-out infinite;
    }
    
    .dot:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    .dot:nth-child(3) {
      animation-delay: 0.4s;
    }
    
    @keyframes loading {
      0%, 60%, 100% {
        transform: scale(0.8);
        opacity: 0.3;
      }
      30% {
        transform: scale(1);
        opacity: 1;
      }
    }
    
    /* 确保聊天框位置调整 */
    .chat-box {
      margin-top: 20px;
    }
  
    /* 历史会话容器样式 */
    .history-container {
      position: absolute;
      top: 0;
      left: -300px; /* 默认在屏幕左侧外 */
      width: 300px;
      height: calc(100vh - 110px);
      background: white;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
      z-index: 100; /* 确保在Ai-content上层 */
      transition: left 0.3s ease; /* 从左到右的动画效果 */
      display: flex;
      flex-direction: column;
    }
    
    /* 显示历史会话容器 */
    .history-container.show {
      left: 0;
    }
    
    /* 历史会话容器头部 */
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #E4E7ED;
      background-color: #FAFAFA;
    }
    
    .history-header h3 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }
    
    /* 历史会话容器内的关闭按钮 */
    .history-header .history-toggle-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #909399;
      border-radius: 4px;
      transition: all 0.3s ease;
    }
    
    .history-header .history-toggle-btn:hover {
      background-color: #F0F0F0;
    }
    
    /* 历史会话列表区域 */
    .history-list {
      flex: 1;
      overflow-y: auto;
      padding: 10px 0;
      /* 自定义滚动条样式 */
    }
    
    /* 自定义滚动条 - Webkit浏览器 */
    .history-list::-webkit-scrollbar {
      width: 6px;
    }
    
    .history-list::-webkit-scrollbar-track {
      background: #F5F7FA;
    }
    
    .history-list::-webkit-scrollbar-thumb {
      background: #C0C4CC;
      border-radius: 3px;
      transition: background 0.3s ease;
    }
    
    .history-list::-webkit-scrollbar-thumb:hover {
      background: #909399;
    }
    
    /* 自定义滚动条 - Firefox浏览器 */
    .history-list {
      scrollbar-width: thin;
      scrollbar-color: #C0C4CC #F5F7FA;
    }
    
    /* 历史会话分组标题 */
    .history-group {
      margin-bottom: 20px;
    }
    
    .history-group-title {
      padding: 0 20px;
      margin-bottom: 10px;
      font-size: 12px;
      color: #909399;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    /* 历史会话项 */
    .history-item {
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }
    
    .history-item:hover {
      background-color: #F5F7FA;
      transform: translateX(2px);
    }
    
    /* 历史会话删除按钮 */
    .history-delete-btn {
      position: absolute;
      right: 40px;
      top: 50%;
      transform: translateY(-50%);
      color: #C0C4CC;
      font-size: 16px;
      cursor: pointer;
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .history-item:hover .history-delete-btn {
      opacity: 1;
    }
    
    .history-delete-btn:hover {
      color: #F56C6C;
    }
    
    /* 历史会话编辑按钮 */
    .history-edit-btn {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #C0C4CC;
      font-size: 16px;
      cursor: pointer;
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .history-item:hover .history-edit-btn {
      opacity: 1;
    }
    
    .history-edit-btn:hover {
      color: #409EFF;
    }
    
    /* 编辑输入框样式 */
    .history-item-edit {
      width: calc(100% - 80px);
    }
    
    .edit-input {
      width: 100%;
      padding: 4px 8px;
      border: 1px solid #409EFF;
      border-radius: 4px;
      font-size: 14px;
      outline: none;
      box-sizing: border-box;
    }
    
    .edit-input:focus {
      border-color: #66b1ff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
    
    /* 历史会话项激活状态 */
    .history-item.active {
      background-color: #E8F3FF;
      border-left: 3px solid #0070FC;
    }
    
    .history-item.active .history-item-content {
      color: #0070FC;
      font-weight: 500;
    }
    
    .history-item-content {
      font-size: 14px;
      color: #333;
      margin-bottom: 4px;
      line-height: 1.5;
      word-break: break-word;
    }
    
    .history-item-time {
      font-size: 12px;
      color: #909399;
    }
    
    /* 历史会话容器底部 */
    .history-footer {
      padding: 15px 20px;
      border-top: 1px solid #E4E7ED;
      text-align: center;
      background-color: #FAFAFA;
    }
    
    .history-tip {
      font-size: 12px;
      color: #909399;
    }
    
    /* 主内容区域的展开按钮 */
    .history-toggle-btn-main {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 80px;
      height: 32px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #333;
      font-size: 12px;
      transition: all 0.3s ease;
      z-index: 10;
    }
    
    .history-toggle-btn-main:hover {
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  </style>