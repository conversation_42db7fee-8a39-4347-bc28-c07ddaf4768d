

<template>
  <div class="pl-login-page">
    <div class="back-img" @click="clearCache">
        <img src="../../assets/public/login-left-img.png" alt="">
    </div>
    <div class="main">
      <div class="title">
        <h3>登录</h3>
      </div>

      <!-- 登录-->
      <section>
        <div class="login-from">
          <el-input placeholder="请输入账号/手机号" ref="phone" auto-complete="off" v-model.trim="loginForm.phone"
            >
            <template #prefix>
              <i class="iconfont icon-yonghu prefix"></i>
            </template>
          </el-input>

          <el-input placeholder="请输入密码" v-model.trim="loginForm.password" auto-complete="new-password"
            :type="passwordShown ? 'text' : 'password'" >
            <template #prefix>
              <i class="iconfont icon-suoding prefix"></i>
            </template>
            <i slot="suffix" class="iconfont suffix" :class="passwordShown ? 'icon-yanjing1' : 'icon-yanjing2'"
              @click="handleTogglePassword('passwordShown')"></i>
          </el-input>

          <div class="code-box">
            <el-input class="code-input" v-model.trim="loginForm.code" placeholder="请输入图形验证码">
              <template #prefix>
                <i class="iconfont icon-yanzhengyanzhengma prefix"></i>
              </template>
            </el-input>
            <div class="code">
              <img title="刷新验证码" :src="`data:image/png;base64,${verifyCode.data}`"
                @click="getVerifyCode" />
            </div>
          </div>
          <!-- <el-select @change="selectCount">
            <el-option label="学校" :value="1"></el-option>
            <el-option label="教师" :value="2"></el-option>
            <el-option label="学生" :value="3"></el-option>
          </el-select> -->
          <el-button class="btn-login" type="primary" v-preventReClick @click="handleLogin">登录</el-button>
          <el-button class="forget-password" type="text" @click="handleResetPassword">忘记密码？</el-button>

        </div>
      </section>
      <!-- 忘记密码 -->
      <div v-if="isShowLogin" class="login-agreement">
        <el-checkbox v-model="rchecked" fill="#2B66FF" text-color="#FFF"><span @click="agreementSt">我已阅读并同意用户服务协议,
            隐私协议</span></el-checkbox>
      </div>
    </div>

     <el-dialog
      title="重置密码"
      append-to-body
      :visible.sync="dialogVisible"
      custom-class="user-dialog"
      width="500px">
        <div class="pass-info">
          <el-form class="pass-form" :model="passForm" ref="form" :rules="rules" label-width="80px">
            <el-form-item label="手机号" prop="pass">
              <el-input v-model="passForm.phone" placeholder="请输入手机号" />
            </el-form-item>
             <el-form-item label="" prop="code">
              <div style="display:flex;align-items:center;justify-content: space-between;">

                <el-input class="code-ipt" v-model="passForm.smsCode" placeholder="请输入验证码"></el-input>
                <el-button class="code-btn" @click="sendMsg('ResetPassword')" :disabled="isSendingCode">{{ isSendingCode ? `${countdown}秒后重新发送` : '短信验证码' }}</el-button>
              </div>
            </el-form-item>
            <el-form-item label="新密码" prop="password">
              <el-input v-model="passForm.password" placeholder="请输入新密码" />
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="passForm.confirmPassword" placeholder="请输入确认密码" />
            </el-form-item>
            <el-form-item class="footer-btn">
              <el-button class="cancle-btn" @click="dialogVisible = false;">取消</el-button>
              <el-button class="save-btn" @click="savePass">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      
    </el-dialog>
  </div>
</template>
<script>
import { CLASSROOM_ID, setCookies } from "@/utils/cookies";
import doTokenSuccess from "@/utils/doTokenSuccess";
import token from "@/utils/token";
import { uuid } from '@/utils/base'
import { resetRouters } from "@/router/index";
import { mapGetters } from 'vuex'
export default {
  name: "Xlogin",
  components: {  },
  data() {
    return {
      loginForm: {
        phone: "admin", // 用户名
        password: "123456", // 密码,
        code: "", //验证码
        activationCode: ""
      },
      countdown: 0, // 验证码倒计时
      isSendingCode: false, // 是否正在发送验证码
      verifyCode: {
        id: 1,
        data: null
      },
      userInfoObj: {},//用户信息
      userType:'PlatformAdmin', //用户类型 1学校 2教师 3学生
      dialogVisible:false,// 忘记密码弹窗
      passForm:{
        phone:"",
        smsCode:"",
        password:"",
        confirmPassword:""
      },
      rules: {
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { min: 11, max: 11, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        smsCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度必须在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (value !== this.passForm.password) {
              callback(new Error('两次输入密码不一致'));
            } else {
              callback();
            }
          }, trigger: 'blur' }
        ]
      },
    };
  },
  mounted() {
    this.getVerifyCode(); // 获取验证码
  },
  computed: {

  },
  beforeDestroy() {
    
  },
  watch: {
    
  },
  methods: {
    clearCache() {
      window.location.reload(true);
    },
    // 选择账号
    selectCount(val){
      if(val == 1){ //学校
        this.loginForm.phone = "admin"
        this.loginForm.password = "123456"
      }else if(val == 2){ //教师
        this.loginForm.phone = "17600000000"
        this.loginForm.password = "123456"
      }else if(val == 3){ //学生
        this.loginForm.phone = "student"
        this.loginForm.password = "123456"
      }
    },
    handleTogglePassword(val) {
      this[val] = !this[val];
    },
    keyDown(e) {
      if (!this.isShowLogin) return
      if (e.keyCode == 13 && e.target.tagName != "BUTTON") {
        if (this.status == "login") {
          this.handleLogin(); // 定义的登录方法
        }
      }
    },
    async getVerifyCode() {
      if (this.readOnly) {
        return;
      }
      const { errCode, data } = await this.$api.GetVerifyCode({
        type: 0, //0普通验证码 1表达式验证码
        length: 4, //验证码长度
        width: 110,
        height: 36,
        pid: this.verifyCode.id,
        t: Date.now()
      });
      if (errCode === 0) {
        this.verifyCode = data;

        console.log(this.verifyCode)
        this.loginForm.code = "";
        this.$refs.vcode?.focus();
      }
    },
    // 登录
    async handleLogin() {
      // if (this.loginForm.phone == "") {
      //   this.msgModule("请输入账号！", "error");
      //   return;
      // }
      // if (this.loginForm.password == "") {
      //   this.msgModule("请输入密码！", "error");
      //   return;
      // }
      // if (this.loginForm.code == "") {
      //   this.msgModule("请输入验证码！", "error");
      //   return;
      // }
      // if (!this.rchecked) {
      //   this.msgModule("请勾选用户服务协议！", "error");
      //   return;
      // }


      const res = await this.$api.Login({
        phone: this.loginForm.phone,
        password: this.loginForm.password,
        verifyCodeId: this.verifyCode.id,
        verifyCode: this.loginForm.code
      });
      if (res.errCode != 0) {
        this.getVerifyCode(); // 验证码错误，重新获取验证码
        return;
      }
      this.$store.commit("userInfo", res.data);
      sessionStorage.setItem("userInfo", JSON.stringify(res.data));
      token.setToken(res.data.user.accessToken);

     this.userType = res.data.user.userType
     let userType = this.userType.split(',')

      if(this.loginForm.phone =="15202581302"){ // 教师
        this.$router.push({ path: '/student' })
        return
      }
      switch (userType[0]) {
        case 'PlatformAdmin': //平台管理员
          this.$router.push({ path: '/platform' })
        break;
        case 'PlatformOperator': //学校
        case 'SchoolEmployee': //教务
        case 'SchoolAdmin': //学校管理员
          this.$router.push({ path: '/school' })
        break;
        case 'Teacher': //教师
          this.$router.push({ path: '/teacher' })
        break;
        case 'Student': //学生
        case 'PlatformStudent': //学生
          this.$router.push({ path: '/student' })
        break;
      }

    },
    //协议
    agreementSt() {
      this.agreementStutas = true;
    },
    //确认协议
    confirmProtocol() {
      this.notTabsStatus = false;
      this.status = "login";
    },

    //手机号格式校验
    validatePhoneNumber(phone) {
      // 正则表达式进行手机号格式校验
      var regExp = /^1[3456789]\d{9}$/;
      if (regExp.test(phone)) {
        return true; // 符合手机号格式要求
      } else {
        return false; // 不符合手机号格式要求
      }
    },
    //倒计时
    startCountdown() {
      this.isSendingCode = true;
      this.countdown = 60; // 设置倒计时为60秒
      const timer = setInterval(() => {
        this.countdown--; // 每次间隔1秒将倒计时数量减少1
        if (this.countdown <= 0) {
          clearInterval(timer); // 当倒计时结束后清除定时器
          this.isSendingCode = false;
        }
      }, 1000);
    },
    //发送验证码
    async SendVerificationCode() {
      if (this.registerForm.mb === "") {
        this.$message("请输入手机号");
        return;
      }
      if (!this.validatePhoneNumber(this.registerForm.mb)) {
        this.$message.error("手机号格式错误!");
        return;
      }
      const res = await this.$api.VerifyPhoneNumber({
        mb: this.registerForm.mb
      });
      if (!res.code) {
        this.$message.error("手机号已存在!");
        return;
      }
      // const res1 = await this.$api.SendVerificationCode({
      //   phone: this.registerForm.mb
      // });
      // if (res1.code === 200) {
      //   this.startCountdown();
      // }
    },
    // 重置密码
    handleResetPassword(){
      this.dialogVisible = true;
    },
    sendMsg(type){
      if (this.passForm.phone === "") {
        this.$message("请输入手机号");
        return;
      }
      if (!this.validatePhoneNumber(this.passForm.phone)) {
        this.$message.error("手机号格式错误!");
        return;
      }
      // 在这里处理表单提交逻辑，例如发送请求保存用户信息
      this.$api.SendPhoneVerifyCode({phone:this.passForm.phone,type: type || 'ResetPassword'}).then(res=>{
        if(res.errCode==0){
          this.$message({
            message: '验证码发送成功',
            type: 'success'
          });
          this.startCountdown(); // 开始倒计时
        }
      })
    },
    savePass(){
      this.$api.ResetPassword(this.passForm).then(res=>{
        if(res.errCode==0){
          this.$message({
            message: '重置成功',
            type: 'success'
          });
          this.dialogVisible=false;
        }

      })
         
    },

  }
};
</script>

<style lang="scss" scoped>
.pl-login-page {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url("../../assets/public/login-bg.png") no-repeat center/cover;

  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important
  }

  ::v-deep input[type='number'] {
    -moz-appearance: textfield !important
  }

  .back-img {
    width: 900px;
    height: 530px;
  }

  .main {
    position: relative;
    width: 420px;
    background: #fff;
    margin-left: 64px;
    padding:40px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 2px 2px 10px 0px rgba(3,38,80,0.1);
    border-radius: 12px;

    .agreement {
      height: 70vh;
      padding: 0 10px;
      overflow-y: auto;
      margin-bottom: 100px;
    }

    .btn-confirm {
      width: 340px;
      display: block;
      height: 44px;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      background: linear-gradient(270deg,
          #5877fb 0%,
          #114bfa 81%,
          #0f4afa 83%,
          #0041fa 100%);
      box-shadow: 0px 3px 6px 1px rgba(0, 65, 250, 0.42);
      color: #fff;
      border-radius: 30px;
      margin-top: 10px;

      &:hover {
        color: #fff;
      }
    }

    .title {
      width: 100%;
      position: relative;
      text-align: left;
      margin-top: 20px;
      h3{
        margin:0;
      }
    }

    .tabs {
      width: 210px;
      display: flex;
      justify-content: space-between;
      // justify-content: center;
      margin: 0 auto;

      .tabs-item {
        color: #333333;
        font-weight: 400;
        font-size: 16px;
        margin-top: 18px;
        cursor: pointer;
      }

      .active {
        position: relative;

        &::before {
          content: "";
          width: 40px;
          height: 10px;
          // background: #3080f4;
          // background: #DEE7FD;
          background: #6161F7;
          border-radius: 5px;
          position: absolute;
          top: 11px;
          left: -4px;
          opacity: 0.54;
        }
      }
    }

    .login-from,
    .register-from {
      width: 340px;
      margin-top: 30px;

      .el-select {
        width: 100%;
      }

      .el-select,
      .el-input {
        margin-bottom: 20px;
      }

      ::v-deep .el-input__inner {
        border-radius: 4px;
        padding-left: 50px;
        height: 44px;
        border: none;
        background: #fff;
        border: 1px solid #E7E7E7;
        &:focus{
          border: 1px solid #0070FC;
        }
        // background: linear-gradient( 270deg, #F2F5FF 0%, #DDE6FD 100%);
      }

      .code-box {
        display: flex;
        justify-content: space-between;

        .code-input {
          width: 254px;
        }

        .code {
          width: 115px;
          height: 44px;
          border-radius: 30px;
          display: flex;
          align-items: center;

          img {
            cursor: pointer;
            // margin-top: 6px;
          }

          .question {
            display: inline-block;
            font-size: 20px;
            margin-left: 10px;
          }
        }

        .sending-code {

          width: 120px;
          height: 44px;
          border-radius: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          // background-color: #3e67fa;
          background-color: #6161F7;
          color: #ffffff;
          font-size: 14px;
          cursor: pointer;
          caret-color: transparent;
        }
      }

      .suffix {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        line-height: 0.5;
        font-size: 20px;
        font-weight: 500;
        color: var(--color-primary3);
        cursor: pointer;
      }

      .prefix {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        line-height: 0.5;
        font-size: 16px;
        font-weight: 500;
        color: var(--color-primary3);
      }

      .btn-login {
        position: relative;
        width: 340px;
        height: 44px;
        display: block;
        font-size: 18px;
        font-weight: 500;
        // background: linear-gradient( 90deg, #2C66FA 0%, #985BF3 100%);
        
        border-radius: 4px;
        box-shadow: 0px 3px 6px 1px rgba(228, 231, 237, 0.42);
        color: #fff;
        border-radius: 4px;
        margin-top: 10px;
        background: linear-gradient( 90deg, #36B5FB 0%, #3C70F6 100%);
        background: #0070FC;
        // filter: blur(1px);
        &:hover {
          color: #fff;
        }
      }

      .forget-password {
        float: right;
        margin-right: 20px;
      }
    }

    .login-agreement {
      margin-top: 20px;
    }

    .footer-text {
      position: absolute;
      gap: 10px;
      bottom: 20px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      font-size: 12px;
      color: #333;

      .download-chrome {
        display: flex;
        align-items: center;

        span {
          opacity: 0.6;
          margin-left: 5px;
          cursor: pointer;
          text-decoration: underline;

          &:hover {
            color: var(--theme_primary_color);
          }
        }
      }

      .text {
        opacity: 0.6;
      }
    }
  }
}

::v-deep .weak-password-dialog {
  .el-dialog {
    border-radius: 10px;
  }

  .el-dialog__body {
    padding: 0 50px;
  }

  .el-dialog__header {
    display: none;
  }
}

// 字体动画
@keyframes flowCss {
  0% {
    /* 移动背景位置 */
    background-position: 0 0;
  }
  100% {
    background-position: -400% 0;
  }
}
@keyframes upDown {
  0% {
    /* 移动背景位置 */
    transform: translateY(0);
  }
  50% {
    transform: translateY(26%);
  }
  100%{
    transform: translateY(0%);
  }
}
@keyframes upDown2 {
  0% {
    /* 移动背景位置 */
    transform: translateY(0);
  }
  50% {
    transform: translateY(80%);
  }
  100%{
    transform: translateY(0%);
  }
}
@keyframes downUp {
  0% {
    /* 移动背景位置 */
    transform: translateY(0%);
  }
  50% {
    transform: translateY(-20%);
  }
  100%{
    transform: translateY(0%);
  }
}
@keyframes downUp2 {
  0% {
    /* 移动背景位置 */
    transform: translateY(0%);
  }
  50% {
    transform: translateY(-80%);
  }
  100%{
    transform: translateY(0%);
  }
}
@keyframes heightScale {
  0% {
    transform: scaleY(1); /* 初始高度缩放比例为 1 */
  }
  50% {
    transform: scaleY(1.1); /* 高度缩放为原来的 1.1 倍 */
  }
  100% {
    transform: scaleY(1); /* 恢复到初始高度缩放比例 */
  }
}
@keyframes heightScale2 {
  0% {
    transform: scaleY(1); /* 初始高度缩放比例为 1 */
  }
  50% {
    transform: scaleY(1.15); /* 高度缩放为原来的 1.15 倍 */
  }
  100% {
    transform: scaleY(1); /* 恢复到初始高度缩放比例 */
  }
}

</style>
<style lang="scss">
.user-dialog{
    background: linear-gradient( 180deg, #DAFFF5 0%, #FFFFFF 10%);
    .el-dialog__body{
      padding-top: 0;
      padding-bottom: 0;
    }
    .footer-btn{
      border-top: 1px solid #F2F3F5;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 39px;
      height: 58px;
      .save-btn{
        width: 80px;
        color: #FFFFFF;
        height: 38px;
        background: #0070FC;
        border-radius: 4px;
      }
      .cancle-btn{
        width: 80px;
        height: 38px;
        color: #333;
        background: #FFFFFF;
        border-radius: 4px;
      }
    }
    .pass-info{
      .pass-form{
        margin-top: 12px;
        .el-form-item__label{
          line-height: 36px;
          color: #333333;
        }
        .el-form-item{
          margin-bottom: 15px!important;

        }
      }

      .code-ipt{
        width:252px;
      }
    }

}
</style>