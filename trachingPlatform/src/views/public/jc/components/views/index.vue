<template>
    <div >
        <iframe ref="iframePreview" width="100%" height="100%" style="border: none" :srcdoc="iframeContent"
            @load="onIframeLoad"></iframe>
    </div>
</template>

<script>
import Image from './Image.js';
import Carousel from './Carousel.js';
import FoldPanel from './FoldPanel.js';
import CuIframe from './CuH5.js';
import Literature from './Literature.js';
import Note from './Note.js';
import api from "@/api";

export default {
    components: {
    },
    props: {
        value: {  // 接收父组件传递的内容
            type: String,
            default: ''
        }
    },
    data() {
        return {
            iframeContent: '',  // 存储动态生成的 iframe 内容
        };
    },
    watch: {
        // 监控 value 变化，重新渲染 iframe 内容
        value(newContent) {
            this.updateIframeContent(newContent);
        },
    },
    mounted() {

    },
    methods: {
        // 更新 iframe 内容
        updateIframeContent(content) {
            if(!content) {
            this.iframeContent = `
          <html>
            <head>
            </head>
            <body>
            </body>
          </html>
        `;
                return
            }

            let iframeHtml = `
          <html>
            <head>
              <style>
              body {
                    padding:40px 70px;
                }
                /* 添加轮播图的CSS样式 */
                ${Carousel.getCss()}
                /* 折叠面板的CSS样式 */
                ${FoldPanel.getCss()}
                /* h5卡片iframe的CSS样式 */
                ${CuIframe.getCss()}
                /* 参考文献的CSS样式 */
                ${Literature.getCss()}
                /* 词典的CSS样式 */
                ${Note.getCss()}
              </style>
            </head>
            <body>
              ${content}
              <script>
              /* 动态图片预览的HTML代码 */
                ${Image.getJs()}
                /* 动态插入轮播图的HTML代码 */
                ${Carousel.getJs()}
                /* 动态插入折叠面板的HTML代码 */
                ${FoldPanel.getJs()}
                /* 动态插入h5卡片iframe的HTML代码 */
                ${CuIframe.getJs()}
                /* 动态插入参考文献的HTML代码 */
                ${Literature.getJs()}
                /* 词典的JS代码 */
                ${Note.getJs()}
              <\/script>
            </body>
          </html>
        `;

            // 动态更新 iframe 内容
            this.iframeContent = iframeHtml;
        },
        onIframeLoad() {
            console.log('Iframe content loaded!');
            this.$emit('iframeLoaded', this.$refs.iframePreview);

            // 添加消息监听器，处理来自iframe的请求
            window.addEventListener('message', this.handleIframeMessage);
        },

        // 处理来自iframe的消息
        handleIframeMessage(event) {
            // 确保消息来自我们的iframe
            if (event.source !== this.$refs.iframePreview?.contentWindow) return;

            // 处理获取词条详情的请求
            if (event.data && event.data.type === 'getNoteDetail') {
                const { id, messageId } = event.data;

                // 调用接口获取词条详情
                this.fetchNoteDetail(id).then(result => {
                    // 将结果发送回iframe
                    this.$refs.iframePreview.contentWindow.postMessage({
                        type: 'noteDetailResponse',
                        messageId: messageId,
                        result: result
                    }, '*');
                }).catch(error => {
                    console.error('获取词条详情失败:', error);
                    // 发送错误信息回iframe
                    this.$refs.iframePreview.contentWindow.postMessage({
                        type: 'noteDetailResponse',
                        messageId: messageId,
                        result: {
                            errCode: -1,
                            errMsg: error.message || '获取词条详情失败',
                            data: null
                        }
                    }, '*');
                });
            }

            // 处理图片预览请求
            if (event.data && event.data.type === 'previewImage') {
                const { src, messageId } = event.data;

                // 创建全屏预览层
                const previewOverlay = document.createElement('div');
                previewOverlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 9999;';

                // 创建预览容器
                const previewContainer = document.createElement('div');
                previewContainer.style.cssText = 'position: relative; max-width: 90%; max-height: 90%; display: flex; justify-content: center; align-items: center;';

                // 创建预览图片
                const previewImg = document.createElement('img');
                previewImg.src = src;
                previewImg.style.cssText = 'max-width: 100%; max-height: 100%; object-fit: contain;';

                // 创建关闭按钮
                const closeBtn = document.createElement('div');
                closeBtn.innerHTML = '×';
                closeBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; color: white; font-size: 30px; cursor: pointer; width: 40px; height: 40px; line-height: 40px; text-align: center; background-color: rgba(0, 0, 0, 0.5); border-radius: 50%; z-index: 10001;';

                // 添加关闭按钮点击事件
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    document.body.removeChild(previewOverlay);
                });

                // 添加点击遮罩层关闭预览
                previewOverlay.addEventListener('click', function() {
                    document.body.removeChild(previewOverlay);
                });

                // 阻止点击图片时关闭预览
                previewContainer.addEventListener('click', function(e) {
                    e.stopPropagation();
                });

                // 组装预览界面
                previewContainer.appendChild(previewImg);
                previewContainer.appendChild(closeBtn);
                previewOverlay.appendChild(previewContainer);

                // 将预览层添加到body
                document.body.appendChild(previewOverlay);
            }

            // 处理轮播图预览请求
            if (event.data && event.data.type === 'previewCarousel') {
                const { urls, currentIndex, messageId } = event.data;

                if (!urls || urls.length === 0) return;

                // 创建全屏预览层
                const previewOverlay = document.createElement('div');
                previewOverlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 9999;';

                // 创建预览容器
                const previewContainer = document.createElement('div');
                previewContainer.style.cssText = 'position: relative; width: 80%; height: 80%; display: flex; flex-direction: column; align-items: center;';

                // 创建预览图片容器
                const previewImgContainer = document.createElement('div');
                previewImgContainer.style.cssText = 'width: 100%; height: 90%; display: flex; justify-content: center; align-items: center; position: relative;';

                // 创建左右箭头
                const prevBtn = document.createElement('div');
                prevBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="32" height="32"><path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8c-16.4 12.8-16.4 37.5 0 50.3l450.8 352.1c5.3 4.1 12.9 0.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z" fill="#ffffff"></path></svg>';
                prevBtn.style.cssText = 'position: absolute; left: 20px; cursor: pointer; z-index: 10000;';

                const nextBtn = document.createElement('div');
                nextBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="32" height="32"><path d="M765.7 486.8L314.9 134.7c-5.3-4.1-12.9-0.4-12.9 6.3v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1c16.4-12.8 16.4-37.6 0-50.4z" fill="#ffffff"></path></svg>';
                nextBtn.style.cssText = 'position: absolute; right: 20px; cursor: pointer; z-index: 10000;';

                // 创建预览图片
                const previewImg = document.createElement('img');
                previewImg.style.cssText = 'max-width: 100%; max-height: 100%; object-fit: contain;';

                // 创建指示器容器
                const indicatorContainer = document.createElement('div');
                indicatorContainer.style.cssText = 'display: flex; justify-content: center; margin-top: 20px;';

                // 当前显示的图片索引
                let currentPreviewIndex = currentIndex || 0;

                // 显示指定索引的图片
                const showPreviewImage = (index) => {
                    if (index < 0) index = urls.length - 1;
                    if (index >= urls.length) index = 0;
                    currentPreviewIndex = index;
                    previewImg.src = urls[index];

                    // 更新指示器状态
                    const indicators = indicatorContainer.querySelectorAll('.preview-indicator');
                    indicators.forEach((indicator, i) => {
                        if (i === index) {
                            indicator.style.backgroundColor = '#ffffff';
                        } else {
                            indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
                        }
                    });
                };

                // 添加左右箭头点击事件
                prevBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showPreviewImage(currentPreviewIndex - 1);
                });

                nextBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showPreviewImage(currentPreviewIndex + 1);
                });

                // 创建指示器
                urls.forEach((_, index) => {
                    const indicator = document.createElement('div');
                    indicator.className = 'preview-indicator';
                    indicator.style.cssText = 'width: 10px; height: 10px; border-radius: 50%; background-color: ' + (index === currentPreviewIndex ? '#ffffff' : 'rgba(255, 255, 255, 0.5)') + '; margin: 0 5px; cursor: pointer;';

                    indicator.addEventListener('click', (e) => {
                        e.stopPropagation();
                        showPreviewImage(index);
                    });

                    indicatorContainer.appendChild(indicator);
                });

                // 显示当前图片
                showPreviewImage(currentPreviewIndex);

                // 组装预览界面
                previewImgContainer.appendChild(prevBtn);
                previewImgContainer.appendChild(previewImg);
                previewImgContainer.appendChild(nextBtn);
                previewContainer.appendChild(previewImgContainer);
                previewContainer.appendChild(indicatorContainer);
                previewOverlay.appendChild(previewContainer);

                // 创建关闭按钮
                const closeBtn = document.createElement('div');
                closeBtn.innerHTML = '×';
                closeBtn.style.cssText = 'position: absolute; top: 10px; right: 10px; color: white; font-size: 30px; cursor: pointer; width: 40px; height: 40px; line-height: 40px; text-align: center; background-color: rgba(0, 0, 0, 0.5); border-radius: 50%; z-index: 10001;';

                // 添加关闭按钮点击事件
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    document.body.removeChild(previewOverlay);
                });

                // 添加点击遮罩层关闭预览
                previewOverlay.addEventListener('click', function(e) {
                    if (e.target === previewOverlay) {
                        document.body.removeChild(previewOverlay);
                    }
                });

                // 将关闭按钮添加到预览层
                previewOverlay.appendChild(closeBtn);

                // 将预览层添加到body
                document.body.appendChild(previewOverlay);
            }
        },

        // 调用接口获取词条详情
        fetchNoteDetail(id) {
            return new Promise((resolve, reject) => {
                // 这里使用axios或其他HTTP客户端调用接口
                // 假设使用this.$http作为HTTP客户端
                api.GetTextbookDictDetail({id}).then(res => {
                        if(res.errCode == 0) {
                        resolve(res.data)
                        } else {
                        resolve({})
                        }
                    }).catch(err => {
                        reject(err)
                    })
            });
        },
    },
};
</script>
