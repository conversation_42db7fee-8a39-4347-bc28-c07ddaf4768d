<template>
  <div class="note-pannel wh-full flex flex-col">
    <div class="w-full h-50px b-b-1px b-b-solid b-b-#F2F3F5 px-20px flex items-center">
      <div class="text-#333333 font-500 text-16px">笔记</div>
    </div>
    <div class="flex-1-hidden flex flex-col py-10px px-20px">
      <div>
        <el-input v-model="searchText" class="w-full" placeholder="搜索笔记"></el-input>
      </div>
      <div class="flex-1-hidden overflow-y-auto">

      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NotePanel",
  data() {
    return {
      searchText: null,
    }
  },
}
</script>
