<template>
  <div v-loading="loading" class="wh-full">
    <el-tree
      ref="treeRef"
      class="h-full chapter-tree py-20px"
      :data="treeData"
      node-key="id"
      default-expand-all
      :props="defaultProps"
      :expand-on-click-node="false"
      :current-node-key="currentNodeKey"
    >
      <template #icon="{ node }">
        <i v-if="node.expanded" class="icon-open"></i>
        <i v-else class="icon-close"></i>
      </template>
      <template #default="{ node, data }">
        <span class="custom-tree-node pr-10px" @click="!isLevel1OrHasChildren(node, data) && handleNodeClick(data)">
          <!-- 添加展开/收起图标，仅对有子级的节点显示 -->
          <span class="node-title-wrapper">
            <i
              v-if="data.children && data.children.length > 0"
              :class="
                node.expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
              "
              class="expand-icon"
              @click.stop="toggleExpand(node)"
            ></i>
            <i v-else-if="node.level === 1" class="expand-icon-placeholder"></i>
            <span
              :class="[
                isLevel1OrHasChildren(node, data)
                  ? 'bold-title'
                  : 'child-title',
              ]"
            >{{ data.title }}</span
            >
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
import store from "@/store"

export default {
  data() {
    return {
      loading: false,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "title",
        value: "id",
      },
      currentNodeKey: null,
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    // 获取树结构第一级的最后一级
    getFirstLevelLastNode(treeData) {
      if (!treeData) return null
      if (treeData && treeData.length && treeData[0].children && treeData[0].children.length) {
        return this.getFirstLevelLastNode(treeData[0].children)
      } else {
        return treeData[0]
      }
    },
    // 获取树形数据
    async getTreeData() {
      try {
        this.loading = true
        const userInfo = store.getters.userInfo
        const res = await this.$api.GetSectionTree({
          textbookId: this.$route.query.id,
          schoolId: userInfo.schools[0].id,
        })
        console.log("getTreeData11 ->", res)
        const firstLevelLastNode = this.getFirstLevelLastNode(res.data)
        console.log("firstLevelLastNode ->", firstLevelLastNode)
        this.handleNodeClick(firstLevelLastNode)
        if (res.errCode === 0) {
          this.treeData = res.data
        }
      } catch (error) {
        console.log("error-GetNoteTree", error)
      } finally {
        this.loading = false
      }
    },
    // 切换节点展开/收起状态
    toggleExpand(node) {
      node.expanded ? node.collapse() : node.expand()
    },
    // 判断是否为一级节点或有子节点
    isLevel1OrHasChildren(node, data) {
      return node.level === 1 || (data.children && data.children.length > 0)
    },
    // 节点点击事件
    handleNodeClick(data) {
      console.log('handleNodeClick data -->', data)
      this.$emit("node-click", data)
      this.currentNodeKey = data.id
      this.$nextTick(() => {
        this.$refs.treeRef && this.$refs.treeRef.setCurrentKey(data.id)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.chapter-tree {
  .custom-tree-node {
    height: 36px;
    line-height: 36px;
    color: #333;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 10px;
  }

  .icon-open {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("~@/assets/images/tree-open.png") no-repeat center;
    position: relative;
    top: 5px;
    margin-right: 6px;
  }
  .icon-close {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("~@/assets/images/tree-close.png") no-repeat center;
    position: relative;
    top: 5px;
    margin-right: 6px;
  }
  .node-title-wrapper {
    display: flex;
    align-items: center;
  }
  .expand-icon-placeholder {
    width: 16px;
    margin-right: 5px;
    display: inline-block;
  }
}
</style>
