<template>
  <div class="wh-full relative">
    <js-view
      ref="jsViewRef"
      v-model="content"
      class="wh-full"
      @iframeLoaded="iframeLoaded"
    />

    <div
      v-show="toolBarShow"
      id="note-editor-toolbar"
      class="toolbar bubble-menu"
    >
      <button data-command="bold" title="加粗" @click.prevent="executeCommand('bold')">
        <i class="i-mingcute-bold-fill"></i>
      </button>
      <button data-command="italic" title="斜体" @click.prevent="executeCommand('italic')">
        <i class="i-mingcute-italic-fill"></i>
      </button>
      <button data-command="underline" title="下划线" @click.prevent="executeCommand('underline')">
        <i class="i-mingcute-underline-fill"></i>
      </button>
      <button data-command="strikeThrough" title="删除线" @click.prevent="executeCommand('strikeThrough')">
        <i class="i-mingcute-strikethrough-fill"></i>
      </button>
      <el-divider direction="vertical"></el-divider>
      <button title="收录词典">
        <i class="i-mingcute-book-2-line"></i>
        <span class="text-12px ml-4px">收录词典</span>
      </button>
      <el-divider direction="vertical"></el-divider>
      <button title="笔记">
        <i class="i-mingcute-edit-4-line"></i>
        <span class="text-12px ml-4px">笔记</span>
      </button>
    </div>
  </div>
</template>

<script>
import jsView from "@/views/public/jc/components/views/index.vue"

export default {
  name: "Editor",
  components: { jsView },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      iframeRef: null,
      iframeDoc: null,
      toolBarShow: false,
      selectionTimeout: null,
    }
  },
  computed: {
    content: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit("input", val)
      },
    },
  },
  methods: {
    // 安全的事件监听器添加
    safeAddEventListener(element, event, handler) {
      try {
        if (element && typeof element.addEventListener === "function") {
          element.addEventListener(event, handler)
          return true
        }
      } catch (e) {
        console.error("添加事件监听器失败:", e)
      }
      return false
    },
    // 处理文本选择
    handleSelection() {
      if (!this.iframeDoc) return
      if (this.selectionTimeout) {
        clearTimeout(this.selectionTimeout)
      }
      this.selectionTimeout = setTimeout(() => {
        try {
          const selection = this.iframeDoc.getSelection()
          const selectedText = selection.toString().trim()
          console.log("selectedText", selectedText)
          this.toolBarShow = !!selectedText
          if (this.toolBarShow) {
            this.updateToolbarButtons()
          }
        } catch (e) {
          console.error("处理选择失败:", e)
        }
      }, 100)
    },
    // 执行格式化命令
    executeCommand(command) {
      try {
        const selection = this.iframeDoc.getSelection()
        if (!selection || selection.toString().trim().length === 0) {
          console.log("没有选中文字，无法执行格式化")
          return
        }
        if (this.iframeRef.contentWindow) {
          this.iframeRef.contentWindow.focus()
        }
        // 保存当前选择范围
        const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null
        const selectedText = range.toString()
        console.log('command --->', command, selectedText, range)
        if (!selectedText) return
        let wrapper
        switch (command) {
          case "bold":
            wrapper = this.iframeDoc.createElement("strong")
            break
          case "italic":
            wrapper = this.iframeDoc.createElement("em")
            break
          case "underline":
            wrapper = this.iframeDoc.createElement("u")
            break
          case "strikeThrough":
            wrapper = this.iframeDoc.createElement("strike")
            break
          default:
            return
        }
        // 检查是否已经有相同的格式
        const parentElement =
          range.commonAncestorContainer.parentElement ||
          range.commonAncestorContainer
        const existingFormat = parentElement.closest(
          wrapper.tagName.toLowerCase()
        )

        if (existingFormat && command !== "removeFormat") {
          // 移除现有格式
          const parent = existingFormat.parentNode
          while (existingFormat.firstChild) {
            parent.insertBefore(existingFormat.firstChild, existingFormat)
          }
          parent.removeChild(existingFormat)
        } else if (command !== "removeFormat") {
          // 添加新格式
          try {
            range.surroundContents(wrapper)
          } catch (e) {
            // 如果surroundContents失败，使用其他方法
            const contents = range.extractContents()
            wrapper.appendChild(contents)
            range.insertNode(wrapper)
          }
        }

        // 重新选择内容
        selection.removeAllRanges()
        const newRange = this.iframeDoc.createRange()
        newRange.selectNodeContents(wrapper)
        selection.addRange(newRange)
        // 更新按钮状态
        setTimeout(() => {
          this.updateToolbarButtons()
        }, 50)
      } catch (e) {
        console.error("执行命令失败:", command, e)
      }
    },
    // 更新工具栏按钮状态
    updateToolbarButtons() {
      if (!this.iframeDoc || !this.toolBarShow) return

      const buttons = document.querySelectorAll(
        "#note-editor-toolbar button[data-command]"
      )

      buttons.forEach(button => {
        const command = button.getAttribute('data-command')
        if (command && command !== 'removeFormat') {
          try {
            const isActive = this.iframeDoc.queryCommandState(command)
            console.log('isActive', isActive)
            button.classList.toggle('is-active', isActive)
          } catch (e) {
            // 忽略查询状态错误
          }
        }
      })
    },
    // iframe加载完成
    iframeLoaded(element) {
      this.iframeRef = element
      const doc = element.contentDocument
      this.iframeDoc = doc
      doc.designMode = "on"
      setTimeout(() => {
        doc.designMode = "off"
        doc.body.contentEditable = "true"
        this.safeAddEventListener(doc, "beforeinput", function (e) {
          // 只允许格式化相关的输入类型
          const allowedInputTypes = [
            "formatBold",
            "formatItalic",
            "formatUnderline",
            "formatStrikeThrough",
            "removeFormat",
          ]

          if (!allowedInputTypes.includes(e.inputType)) {
            e.preventDefault()
          }
        })
        // 阻止粘贴
        this.safeAddEventListener(doc, "paste", function (e) {
          e.preventDefault()
        })
      }, 100)
      this.safeAddEventListener(doc, "mouseup", this.handleSelection)
    },
  },
}
</script>

<style lang="scss" scoped>
.toolbar {
  position: absolute;
  background: #34495e;
  border: none;
  border-radius: 6px;
  padding: 8px;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
}

/* Bubble menu */
.bubble-menu {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  padding: 4px;
  gap: 4px;
  align-items: center;

  button {
    background-color: unset;
    height: 34px;
    min-width: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
    border-radius: 4px;
    color: var(--gray-6);
    cursor: pointer;
    color: #333333;
    font-size: 14px;

    i {
      font-size: 14px;
    }

    &:hover {
      background-color: #edf2ff;
    }

    &.is-active {
      background-color: #edf2ff;
      color: #0070fc;

      &:hover {
        background-color: #d8e2ff;
        color: #0070fc;
      }
    }
  }
}
</style>
