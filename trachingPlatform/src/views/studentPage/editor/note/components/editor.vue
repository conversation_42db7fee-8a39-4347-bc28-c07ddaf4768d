<template>
  <div class="editor wh-full">
    <tinymce-editor
      ref="editor"
      v-model="content"
      :show-toolbar="false"
      quickbars_insert_toolbar="notesNode"
    />
  </div>
</template>

<script>
import TinymceEditor from "@/components/Tinymce/index.vue"
export default {
  name: "Editor",
  components: {
    TinymceEditor,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    nowSection: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      content: this.value,
    }
  },
  watch: {
    value(val) {
      this.content = val
    },
  },
}
</script>
