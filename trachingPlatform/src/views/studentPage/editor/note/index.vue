<template>
  <div class="note-editor wh-full flex flex-col">
    <div class="note-editor__header w-full h-60px px-20px relative">
      <div class="lh-60px w-full text-center font-500 text-#333 text-16px">
        {{ title }}
      </div>
    </div>
    <div
      class="note-editor__wrapper flex-1-hidden w-full flex bg-#F7F7F7 relative"
    >
      <!--  左侧面板    -->
      <div class="left w-280px h-full bg-white absolute left-0 top-0 z-9 transition-transform ease duration-300" :class="leftVisible ? 'translate-x-[-280px]' : ''">
        <div
          class="bg-white hover:bg-#f5f7fa b-1px b-solid b-#E7E7E7 cursor-pointer w-30px h-30px absolute top-80px right-[-30px] cursor-pointer flex items-center justify-center"
          @click="leftVisible = !leftVisible"
        >
          <i
            class="iconfont icon-jiantou_xiangyouliangci text-#666666 text-12px"
            :style="{ transform: `rotate(${leftVisible ? 180 : 0}deg)` }"
          />
        </div>
        <NoteTree @node-click="handleChapterNodeClick" />
      </div>
      <!--   编辑器   -->
      <div class="content flex-1-hidden py-20px">
        <div v-loading="sectionLoading" class="w-840px h-full m-auto bg-white overflow-y-auto">
          <Editor
            ref="editor"
            :value="editorContent"
          />
        </div>
      </div>
      <!--   右侧面板   -->
      <div class="right w-400px h-full bg-white absolute right-0 top-0 z-9">
        <div class="absolute top-50px left-[-32px] w-32px flex flex-col tabs">
          <div
            v-for="item in rightTabs"
            :key="item.key"
            class="tab"
            :class="{ active: rightActiveTab === item.key }"
          >
            {{ item.name }}
          </div>
        </div>

        <NotePanel v-if="rightActiveTab === 'note'" />
      </div>
    </div>
  </div>
</template>

<script>
import Editor from "./components/editor.vue"
import NoteTree from "./components/noteTree.vue"
import NotePanel from "@/views/studentPage/editor/note/components/note-panel.vue";

export default {
  name: "NoteEditor",
  components: {
    Editor,
    NoteTree,
    NotePanel
  },
  data() {
    return {
      title: '未选择章节',
      sectionLoading: false,
      leftVisible: false,
      rightActiveTab: "note",
      rightTabs: [
        { key: "note", name: "笔记" },
        { key: "keyWord", name: "重点词" },
        { key: "reference", name: "参考文献" },
      ],
      editorContent: "",
      currentSelection: null,
      chapterId: null
    }
  },

  methods: {
    // 获取章节教材内容
    async getSectionContent(id) {
      try {
        this.sectionLoading = false
        const res = await this.$api.SectionGetById({ id: this.chapterId })
        if (res.errCode === 0) {
          this.editorContent = res.data.content || ""
        }
      } catch (error) {
        console.log("error-GetSectionContent", error)
      } finally {
        this.sectionLoading = false
      }
    },
    // 章节树形点击事件
    handleChapterNodeClick(data) {
      this.title = data.title
      this.chapterId = data.id
      this.getSectionContent()
    },

    handleContentUpdate(content) {
      this.editorContent = content
      // 这里可以添加自动保存逻辑
      console.log("内容更新:", content)
    },

    handleSelectionChange(selection) {
      this.currentSelection = selection
      console.log("选择变化:", selection)
    },

    handleEditorFocus() {
      console.log("编辑器获得焦点")
    },

    handleEditorBlur() {
      console.log("编辑器失去焦点")
    },

    // 词典收录功能
    handleDictionaryAdd(text) {
      console.log("收录词典:", text)
      // 这里实现词典收录逻辑
      this.$message && this.$message.success(`已收录词汇: ${text}`)
    },

    // 笔记功能
    handleNoteAdd(text) {
      console.log("添加笔记:", text)
      // 这里实现笔记添加逻辑
      this.$message && this.$message.success(`已为 "${text}" 添加笔记`)
    },
  },
}
</script>

<style lang="scss" scoped>
.note-editor {
  width: 100vw;
  height: 100vh;

  &__header {
    position: relative;
    z-index: 10;
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.1);
  }

  &__wrapper {
    .left {
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.05);
    }

    .content {
    }

    .right {
      box-shadow: 1px 1px 12px 0px rgba(51, 51, 51, 0.1);

      .tabs {
        border-radius: 4px 0px 0px 4px;
        overflow: hidden;
        gap: 1px;
        .tab {
          height: 80px;
          line-height: 32px;
          writing-mode: vertical-lr;
          text-align: center;
          background: #e7e7e7;
          font-width: 400;
          font-size: 14px;
          color: #333333;
          cursor: pointer;

          &:hover {
            background: #0070fc;
            color: #fff;
          }

          &.active {
            background: #0070fc;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
