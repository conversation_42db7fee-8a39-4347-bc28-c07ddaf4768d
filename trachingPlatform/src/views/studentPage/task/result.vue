
<template>
  <div class="result-container" :class="{ 'no-score': !allowShowScore }">
    <div class="result-header">
        <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item :to="{ path: '/student/course' }">我的课程</el-breadcrumb-item>
            <el-breadcrumb-item>会计基础</el-breadcrumb-item>
            <el-breadcrumb-item>期末考试</el-breadcrumb-item>
        </el-breadcrumb>
    </div>
    <!-- 顶部分数展示横幅 -->
    <div class="score-banner">
      <div class="score-content">
       
        
        <!-- 根据是否允许展示分数显示不同内容 -->
        <div v-if="allowShowScore" class="score-circle">
            <div class="score-title">恭喜您完成本次任务！</div>
            <div class="score-number">{{score}}<span class="score-unit">分</span></div>
            <div class="score-desc">您的答题得分</div>
        </div>
        
        <!-- 不允许展示分数时显示的内容 -->
        <div v-else class="no-score-content">
          <div class="check-icon">✓</div>
          <div class="score-title">恭喜您完成本次任务！</div>
        </div>
      </div>
    </div>
    
    <!-- 任务信息部分 -->
    <div  class="task-info">
      <div class="info-item" v-if="allowShowScore">
        <span class="info-label">用时</span>
        <span class="info-value">{{usedTime}}</span>
      </div>
      <div class="info-item">
        <span class="info-label">交卷时间</span>
        <span class="info-value">{{submitTime}}</span>
      </div>
      <div class="info-item">
        <span class="info-label">得分率</span>
        <span class="info-value">{{scoreRate}}</span>
      </div>
    </div>
    
    <!-- 操作按钮部分 -->
    <div class="action-buttons">
      <el-button v-if="allowShowScore" class="detail-btn" @click="viewDetails">查看详情</el-button>
      <el-button class="close-btn" @click="closePage">关闭</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskResult',
  data() {
    return {
      // 这里可以根据实际需求从API获取数据
      score: 85,
      usedTime: '1分13秒',
      submitTime: '02-21 16:39',
      scoreRate: '85.0%',
      // 控制是否允许展示分数，false表示不允许展示分数
      allowShowScore: true
    }
  },
  created() {
    // 实际应用中，这里应该从API获取allowShowScore的值
    // 这里仅作为示例，可以根据需求修改
    // this.$api.getTaskResult({ taskId: this.$route.query.taskId }).then(res => {
    //   this.allowShowScore = res.data.allowShowScore;
    //   // 其他数据赋值...
    // })
    const resultInfo = sessionStorage.getItem('resultInfo')
    if(resultInfo){
      const data = JSON.parse(resultInfo)
      this.score = data.scoringRate;
      this.usedTime = data.timeTakenSeconds;
      this.submitTime = data.submitExamTime;
      this.scoreRate = data.scoringRatePercentage;
    }
  },
  methods: {
    // 查看详情按钮点击事件
    viewDetails() {
      // 实现查看详情的逻辑
      console.log('查看详情');
      // 可以根据实际需求跳转到详情页面或显示详情弹窗
    },
    // 关闭按钮点击事件
    closePage() {
      // 实现关闭页面的逻辑
      console.log('关闭页面');
      // 可以返回上一页或关闭当前弹窗
      this.$router.back();
    }
  }
}
</script>

<style scoped lang="scss">
.result-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  padding-top: 0;
  box-sizing: border-box;


    /* 顶部导航栏 */
    .result-header {
        width: 1200px;
        margin: 0 auto;
        height: 60px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 24px;
    }
}

/* 分数横幅样式 */
.score-banner {
  width: 1200px;
  height: 200px;
  background: url('../../../assets/public/grade-banner.png') center/cover no-repeat #1890ff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  position: relative;
}

/* 不显示分数时的内容样式 */
.no-score-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.check-icon {
  font-size: 40px;
  color: #1890ff;
  background-color: rgba(255, 255, 255, 1);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-bottom: 30px;
}

/* 不显示分数时调整按钮位置居中 */
.no-score .action-buttons {
  justify-content: center;
}

.score-content {
  text-align: center;
  color: white;
}

.score-title {
  font-size: 18px;
  margin-bottom: 20px;
}

.score-circle {
  position: relative;
  width: 244px;
  height: 112px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  background: url('../../../assets/public/grade-bg.png') center/cover no-repeat;
  z-index: 2;
  background-size: contain;
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-unit {
  font-size: 16px;
  margin-left: 0px;
}

.score-desc {
  font-size: 12px;
  margin-top: 5px;
}

/* 任务信息样式 */
.task-info {
  width: 100%;
  max-width: 600px;
  display: flex;
  justify-content: space-around;
  margin-bottom: 40px;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item {
  text-align: center;
}

.info-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.info-value {
  display: block;
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 20px;
}

.detail-btn {
  background-color: #07C392;
  border-color: #07C392;
  color: white;
}

.detail-btn:hover {
  background-color: #10D3A0;
  border-color: #10D3A0;
  color: white;
}

.close-btn {
  background-color: #fff;
  border-color: #d9d9d9;
  color: #666;
}

.close-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}
</style>
