<template>
  <div class="exam-container">
    <!-- 顶部导航栏 -->
    <div class="exam-header">
        <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item :to="{ path: '/student/course' }">我的课程</el-breadcrumb-item>
            <el-breadcrumb-item @click="$router.go(-1)">任务列表</el-breadcrumb-item>
            <el-breadcrumb-item>{{ this.$route.query.title }}</el-breadcrumb-item>
        </el-breadcrumb>
    </div>

    <!-- 主内容区域 -->
    <div class="exam-main">
      <!-- 左侧考试信息 -->
      <div class="exam-sidebar">
        <!-- 计时器 -->
        <div class="timer-section">
          <div class="timer-title"><i class="el-icon-time"></i>&nbsp;&nbsp;剩余时间</div>
          <div class="timer-display">
            <div class="timer-circle">
              <div class="timer-time">{{ remainingTime }}</div>
            </div>
          </div>
          <div class="timer-progress">
            <!-- <div class="progress-label">已完成进度</div> -->
            <el-progress :percentage="questionList.length ? (questionList.filter(item => item.isSubmit).length / questionList.length) * 100 : 0" :show-text="false" :stroke-width="10" :color="customColor" class="custom-progress" />
            <div class="progress-text">
              <span class="span1">已完成: {{ questionList.filter(item => item.isSubmit).length }}题</span>
              <span class="span2">共: {{ questionList.length }}题</span>
            </div>
          </div>
        </div>

        <!-- 答题卡 -->
        <div class="answer-card">
          <div class="answer-card-header">
            <span>答题卡</span>
          </div>
          <div class="answer-card-body">
            <div class="answer-card-grid">
              <div class="answer-card-item" @click="getQuestionInfo(item)" :class="getItemStatusClass(item)" v-for="(item,index) in questionList" :key="item.id"> 
                <span class="item-number">{{ index+1 }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 底部操作按钮 -->
        <div class="action-buttons">
          <el-button v-if="!isViewMode" type="primary" size="large" @click="handleSubmit" class="submit-btn">完成</el-button>
        </div>
      </div>

      <!-- 右侧答题区域 -->
      <div class="exam-content">
        <div class="question-section" v-for="(item,index) in questionList" :key="item.id">
          <!-- 题目头部 -->
          <div class="question-header" >
            <div>
              <span class="question-type">【{{ questionTypeLabel[item.type] }}】</span>
              <span class="question-score">{{item.score}}分</span>
            </div>
            <el-button v-if="!isViewMode" type="primary" @click="submitAnswer(item, index)" size="mini">保存</el-button>
          </div>
          <!-- 题目内容 -->
          <div class="question-content" >
            <!-- <questionPreviewContent :showSort="true" :showTiele="false" :sort="item.sort" :data="item" /> -->
              <answer-form-preview
                  :ref="item.id"
                  :type="item.questionType"
                  :config="item.optionConfig"
                  :title="item.title"
                  :desc="item.desc"
                  :answer-value.sync="item.studentAnswer"
                  :error="item.answerCompareInfo"
                  :isDoTask="true"
                  :disable="false"
                  :showAnswer="false"
                  :sort="index"
                  :isshowscore="true"
                  :isshowAnalysis="true"
                  :isshowBtn="true"
                  :isshowDel="false"
                  :gradeId="gradeId"
                  >
                </answer-form-preview>
          </div>

        </div>

        
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {initApiQuestion} from "@/components/question/utils";
import {questionTypeLabel,questionTypeMenu, getAnswerDispatch, getDifficultyDispatch,hasClosedHtmlTags,getInitAnswerValue,getCorrectAnswer} from '@/components/base/question/util.js';
const answerFormPreview = () => import('@/components/base/question/answer-form-preview.vue')
export default {
  name: 'ExamTask',
  components: {
    questionPreviewContent: () => import('@/components/question/question-preview-content.vue'),
    answerFormPreview
  },
  data() {
    return {
      // 当前答案状态
      currentAnswer: '',
      currentAnswer2: '',
      fillAnswer: '',
      selectAnswer: '',
      editorContent: '',
     
      // 已完成题目数
      completedCount: 5,
      // 总题目数
      totalCount: 25,
      customColor:'#0070FC',
      questionList: [],//  题目列表
      gradeId:0, // 作答记录id
       // 计时器相关
      remainingTime: 0, // 剩余时间
      timerId: null, // 计时器ID
      totalSeconds: 0, // 总剩余秒数
      questionTypeLabel,
    }
  },
  computed: {
    // 计算已完成进度百分比
    progressPercentage() {
      return Math.round((this.completedCount / this.totalCount) * 100)
    },
    // 判断是否为查看模式
    isViewMode() {
      return this.$route.query.status === 'view'
    },
    ...mapGetters(['userInfo']),
  },
  mounted() {
    // 初始化任务列表，计时器会在获取到数据后自动启动
    this.initTasskList();
  },
  // 组件销毁前清除计时器
  beforeDestroy() {
    this.stopTimer();
  },
  methods: {
    
    getAnswerDispatch,
    getDifficultyDispatch,
    hasClosedHtmlTags,
    initTasskList(){
      let params = {
        "taskId": this.$route.query.taskId,
        "gradeId": 0,
        "title": this.$route.query.title,
        // "studentId": this.userInfo.user.id,
        "noAutoScore": false
      }
      this.$api.GetStudentTaskQuestionList(params).then(res => {
        if (res.errCode == 0) {

          this.questionList = res.data.questions.map(v => {
            return {
              ...initApiQuestion(v.question),
              score: v.score, // 分值,
              sort: v.sort,
              isSubmit:v.isSubmit,
              gradeDetails:v.gradeDetails||[],
              studentAnswer: v.gradeDetails[0]?.answer,

            }
          }); // 处理题目列表
          this.gradeId = res.data.gradeId;
          console.log("题目列表---------",this.questionList)
          
          // 将剩余时间转换为秒数
          if (res.data.remainingTime) {
            // 这里为了演示，我们假设剩余时间为30分钟
            this.totalSeconds = res.data.remainingTime;
            // 开始倒计时
            if(!this.isViewMode){
              this.startTimer();
            }
          }
        }
      })
    },
    // 获取题目详情
    getQuestionInfo(item){
      let params = {
        QuestionId: item.questionId,
        GradeDetailId: this.gradeId,
        TaskId: this.$route.query.taskId,
      }
      this.$api.GetGradeDetail(params).then(res => {
        if (res.errCode == 0) {
         console.log(res.data)
        }
      })
    },
    // 开始计时器
    startTimer() {
      // 清除之前可能存在的计时器
      if (this.timerId) {
        clearInterval(this.timerId);
      }
      
      // 如果有剩余秒数则开始倒计时
      if (this.totalSeconds > 0) {
        this.updateTimerDisplay();
        
        this.timerId = setInterval(() => {
          this.totalSeconds--;
          this.updateTimerDisplay();
          
          // 时间到，停止计时器并提交答案
          if (this.totalSeconds <= 0) {
            clearInterval(this.timerId);
            this.timerId = null;
            this.$message.warning('考试时间已结束，系统将自动提交答案');
            this.handleSubmit();
          }
        }, 1000);
      }
    },
    
    // 更新计时器显示
    updateTimerDisplay() {
      // 计算小时、分钟、秒
      const hours = Math.floor(this.totalSeconds / 3600);
      const minutes = Math.floor((this.totalSeconds % 3600) / 60);
      const seconds = this.totalSeconds % 60;
      
      // 格式化为 HH:mm:ss
      this.remainingTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },
    
    // 停止计时器
    stopTimer() {
      if (this.timerId) {
        clearInterval(this.timerId);
        this.timerId = null;
      }
    },
    // 获取题目状态样式
    getItemStatusClass(item) {
      if (item.isSubmit) {
        return 'status-completed'
      } else if (status === 2) {
        return 'status-marked'
      }
      return ''
    },
    // 提交答案
    async submitAnswer(qsInfo,index) {
        const answer = this.getQuestionAnswer(qsInfo);
        if (!answer) return this.$message({ type: "warning", message: "请填写答案后提交" });
        const params = {
          id: qsInfo.gradeDetails ? qsInfo?.gradeDetails[0]?.id : qsInfo.gradeDetailsId || 0,
          gradeId: this.gradeId,
          questionId: qsInfo.id,
          questionType: qsInfo.questionType,
          answer:answer,
          noAutoScore: qsInfo.questionType=='ShortAnswer'?true:false,//是否自动判分
          isReadonly: false,
          taskId: this.$route.query.taskId,
        };
        const { data, errCode,msg } = await this.$api.SubmitAnswer(params);
        if (errCode === 0) {
          this.$message({
            type: "success",
            message: "提交成功！"
          });
          qsInfo.gradeDetailsId = data.id; // 作答记录id
          // // // 更新当前题的作答记录
          // if(questionTypeLabel[qsInfo.questionType]){
          //   qsInfo.gradeDetails = [
          //     {
          //       ...data,
          //       answer:params.answer,
          //     }
          //   ]
          // }
          // // 得分显示
          // this.handleQuestionScore(qsInfo.showScore, data, qsInfo.isReadonly,qsInfo.questionType);
          // // 处理标红处理
          // this.handleQuestionCompare(qsInfo.questionType, qsInfo.showError, data, qsInfo.isReadonly);
          // // 更改题目列表状态
          // this.changeQuestionStatus(data.questionsId, data.sumScore);
          // // 更改题目作答状态
          // this.changeSingleQuestionStatus(qsInfo, index);
        } else if (code === 1000) { // 题目已更新 需要刷新当前题目
          // 获取单个题目信息
          // this.openQuestionDetail(qsInfo, index);
       
        }else if(code==1001){ // 题目答案格式不对的问题
          let msgList = msg.split('__')
          console.log('msg', msgList);
        }
      // this.$confirm('确定要提交当前答案吗？提交后将无法修改。', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   // 提交答案的逻辑
      //   this.$message({ type: 'success', message: '答案提交成功' })
      // }).catch(() => {
      //   this.$message({ type: 'info', message: '已取消提交' })
      // })
    },
    // 处理提交
    handleSubmit() {
      this.$confirm('确定要提交当前任务吗？提交后将无法修改。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then( async () => {
        // 提交答案的逻辑
        const { data,errCode, msg } = await this.$api.SubmitExam({
          gradeId:[this.gradeId],
        })
        if (errCode === 0) {
          // console.log('data-----', data);
          this.$message({ type: 'success', message: '答案提交成功' })
          sessionStorage.setItem('resultInfo',JSON.stringify(data))
          this.$router.push({ path: '/student/task/result' })
        } else {
          this.$message({ type: 'error', message: msg || '答案提交失败' })
        }
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消提交' })
      })
    },
    // 获取题目的答案
    getQuestionAnswer(qsInfo) {
      // 题库
      if (questionTypeLabel[qsInfo.questionType]) {
        let answer = null;
        // 填空题特殊处理
        if (qsInfo.questionType === questionTypeMenu.content) { // 填空
          const courseContents = sessionStorage.getItem('courseContents')
          answer = getCorrectAnswer(questionTypeMenu.content, {
            ...qsInfo.optionConfig,
            settingArr: courseContents ? JSON.parse(courseContents) : []
          })
        }else if(qsInfo.questionType === questionTypeMenu.shortAnswer){// 简答题
          qsInfo.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.shortEditor.html;
          answer = getCorrectAnswer(questionTypeMenu.shortAnswer, {
            ...qsInfo.optionConfig,
            settingArr: []
          })
          return JSON.stringify(answer)
        } else{
          answer = qsInfo.studentAnswer
        }
        
        return JSON.stringify({
          longArray: typeof getInitAnswerValue(qsInfo.questionType) === 'object' ? answer : [answer]
        })
      }
      let questionId = qsInfo.id;
      const data = this.$refs[questionId][0].getAnswer();
      return JSON.stringify(data);
    },
  }
}
</script>

  <style lang="scss" scoped>
.exam-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.exam-header {
    width: 1200px;
    margin: 0 auto;
    height: 60px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 24px;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}
.user-id {
  color: #606266;
}

/* 主内容区域 */
.exam-main {
    width:1200px;
    flex: 1;
    display: flex;
    padding: 20px;
    padding-top: 0;
    gap: 20px;
    overflow: hidden;
    margin: 0 auto;
}

/* 左侧边栏 */
.exam-sidebar {
  width: 260px;
  background-color: #f5f7fa;
  border-radius: 8px;
//   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
}

/* 计时器区域 */
.timer-section {
    margin-bottom: 10px;
    background: #fff;
    border: 1px solid transparent;
    padding-bottom: 10px;
}

.timer-title {
  font-size: 16px;
  font-weight: bold;
  color: #666;
  text-align: center;
  padding: 30px 20px 20px;
 
}

.timer-circle {
  width: 180px;
  height: 40px;
  /* background-color: #f0f2f5; */
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 15px;
  position: relative;
}

/* .timer-circle::before {
  content: '';
  position: absolute;
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 4px solid #409eff;
} */

.timer-time {
  font-size: 30px;
  font-weight: bold;
  color: #FFAB2B;
  z-index: 1;
}
.timer-display{
    padding-bottom:10px;
}
.timer-progress {
  width: 80%;
  margin:20px auto;
}

.progress-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.custom-progress .el-progress-bar__inner {
  background-color: #0070FC
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #999;
  margin-top: 16px;
  .span1 {
    color: #0070FC;
  }
    
}

/* 答题卡 */
.answer-card {
//   border-top: 1px solid #e4e7ed;
  background: #fff;
}
.answer-card-body{
    padding: 20px;
}

.answer-card-header {
  font-size: 15px;
  font-weight: bold;
  height: 40px;
  line-height: 40px;
  padding-left: 26px;
  color: #303133;
  margin-bottom: 15px;
  border-bottom:1px solid #e4e7ed;
  position: relative;
  &::before{
    content: "";
    position: absolute;
    left: 14px;
    top: 12px;
    width: 4px;
    height: 14px;
    background-color: #0070FC;
  }
}

.answer-card-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 6px;
  min-height: 200px;
}

.answer-card-item {
  position: relative;
  width: 26px;
  height: 26px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999999;
  &:hover{
    background: #F3F8FF;
    border-radius: 4px;
    border: 1px solid #7AAFF2;
  }
   &.active{
      background: #F3F8FF;
      border-radius: 4px;
      border: 1px solid #7AAFF2;
  }
}

.item-number {
  font-size: 14px;
}

.status-completed {
  background: #F3F8FF;
  border-radius: 4px;
  border: 1px solid #7AAFF2;
  color: #0070FC
}

.status-marked {
  background: #f56c6c;
  border-radius: 4px;
  border: 1px solid #f56c6c;
  color: #fff;
}

/* 右侧答题区域 */
.exam-content {
  flex: 1;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 题目区域 */
.question-section {
  flex: 1;
}

.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  justify-content: space-between;
  ::v-deep .el-button{
    background:#07C392;
    border-color:#07C392;
  }
}

.question-type {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.question-score {
  font-size: 16px;
  color: #333;
  padding: 2px 8px;
}

.question-content {
  margin-bottom: 20px;
}

.question-text {
  font-size: 16px;
  color: #303133;
  line-height: 1.8;
  margin-bottom: 15px;
}

/* 选项样式 */
.question-options {
  margin-left: 20px;
}

.option-item {
  display: block;
  margin-bottom: 10px;
}

/* 输入框样式 */
.question-answer-input {
  margin-top: 10px;
}

.question-answer-input .el-input__inner {
  border-radius: 4px;
}

/* 编辑器工具栏 */
.editor-toolbar {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.editor-toolbar .el-button--text {
  color: #606266;
}

.editor-toolbar .el-divider {
  height: 20px;
}

.editor-content .el-textarea__inner {
  border-radius: 0 0 4px 4px;
  min-height: 120px;
}

/* 底部操作按钮 */
.action-buttons {
  margin-top: 10px;
  text-align: center;

}

.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  background-color: #07C392;
  border-color: #07C392;
}

.submit-btn:hover {
  background-color: #10D3A0;
  border-color: #10D3A0;
}

/* 间距样式 */
.mt-4 {
  margin-top: 20px;
}
</style>