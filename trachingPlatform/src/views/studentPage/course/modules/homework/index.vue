<template>
  <div class="student-home-work-page">
    <div class="top-tool">
        <div class="left">
            <el-input class="serach-input" clearable @change="initList()" v-model="searchParams.Name" placeholder="请输入任务名称" suffix-icon="iconfont icon-sousuo"></el-input>
            <el-select class="select-status" clearable @change="initList()" v-model="searchParams.Status" placeholder="请选择作业状态">
                <el-option v-for="item in statusList" :key="item.id" :label="item.name" :value="item.value"></el-option>
            </el-select>
            <el-button class="search-btn" type="primary">搜索</el-button>
            <el-button class="reset-btn" type="default">重置</el-button>
        </div>
    </div>
    <div class="task-list">
          <!-- 使用 Element UI 栅格系统 -->
          <el-row :gutter="10" v-if="taskList.length>0">
            <el-col :span="24" v-for="item in taskList" :key="item.id">
                <div class="task-item">
                    <p :class="getTaskStatusClass(item.status)">{{getTaskStatusText(item.status,item.isSubmit)}}</p>
                    <h3 class="task-title">{{item.name}}   
                      <!-- <span class="label-1">课后巩固</span> -->
                    </h3>
                    <!-- <p class="chapter-info">所属章节：课程设计 > 课程标准</p> -->
                    <div class="task-info">
                        <el-row class="left-info">
                            <el-col :span="24" class="info-item">
                                <p class="lab">时间</p>  &nbsp;:&nbsp;
                                <p class="val">{{formatISOString(item.beginTime)}}->{{formatISOString(item.endTime)}}</p>
                            </el-col>
                            <el-col :span="8" class="info-item">
                                <p class="lab">题量</p>  &nbsp;:&nbsp;
                                <p class="val">{{item.questionCount}}</p>
                            </el-col>
                            <el-col :span="8" class="info-item">
                                <p class="lab">卷面分</p>  &nbsp;:&nbsp;
                                <p class="val">{{item.totalScore}}</p>
                            </el-col>
                            <el-col :span="8" class="info-item">
                                <p class="lab">得分</p>  &nbsp;:&nbsp;
                                <p class="val">{{item.totalScore}}</p>
                            </el-col>
                           
                        </el-row>
                        <div class="operate-box">
                            <el-button v-if="item.status=='InProgress'&&!item.isSubmit" class="remark-btn" @click="startHomeWork(item)">立即作答</el-button>
                            <el-button v-if="item.status=='InProgress'&&item.isSubmit" class="remark-btn" @click="startHomeWork(item)">查看作答</el-button>
                            <el-button v-if="item.status=='Finished'" class="static-btn" @click="statistics(item)">作答卷面</el-button>
                        </div>
                    </div>
                    <p class="task-time">
                        <span class="remark">备注：{{item.description}}</span>
                    </p>
                    
                </div>
            </el-col>
        </el-row>
        <empty style="margin-top:100px;"  v-if="taskList.length==0" msg="暂无数据" size="middle" />
    </div>
  </div>
</template>
  
<script>
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
export default {
    name: 'examTaskList',
    data() {
        return {
            taskName: '',
            taskStatus: '',
            statusList:[
                {id:1,name:'未开始',value:'NotStarted'},
                {id:2,name:'进行中',vallue:'InProgress'},
                {id:3,name:'已完成',value:'Finished'}
            ],
            taskTypeList:[
                {id:1,name:'课后巩固',value:'AfterClass'},
                {id:2,name:'课后测试',value:'AfterClassTest'},
                {id:3,name:'课后作业',value:'AfterClassHomework'}
            ],
            taskList: [], // 任务列表
            searchParams: { // 搜索参数
                CourseId: this.$route.query.courseId, // 课程ID
                Name: '', // 任务名称
                TaskCategory:'作业', // 任务类型 作业||考试
                TagType:'', // 考试类型 课前预习, 课中授课, 课后巩固
                Status: '', // 任务状态
                PageIndex: 1, // 页码
                PageSize: 10 // 每页数量
            }
        } 
    },
    components: {
        // 引入组件
        empty:()=>import("@/components/base/empty.vue"),
    },
    mounted() {
        this.initList(); // 初始化任务列表
    },
    methods: {
        formatISOString,
        // 获取任务状态对应的CSS类名
        getTaskStatusClass(status) {
            const baseClass = 'task-label';
            let statusClass = '';
            switch(status) {
                case 1:
                    statusClass = 'status1';
                    break;
                case 2:
                    statusClass = 'status2';
                    break;
                case 3:
                    statusClass = 'status3';
                    break;
            }
            return [baseClass, statusClass].join(' ');
        },
        // 获取任务状态对应的文本
        getTaskStatusText(status,isSubmit) {
            switch(status) {
                case 'NotStarted':
                    return '未开始';
                case 'InProgress':
                    return  isSubmit?'已提交':'进行中';
                case 'Finished':
                    return '已完成';
                default:
                    return '';
            }
        },
        initList() {
             /*
            this.taskList = [
                {
                    id: 1,
                    name: 'JavaScript基础语法练习',
                    status: 1, // 未开始
                    beginTime: '2023-11-10T08:00:00',
                    endTime: '2023-11-20T23:59:59',
                    questionCount: 20,
                    totalScore: 100,
                    submittedCount: 0,
                    unsubmittedCount: 30,
                    pendingReviewCount: 0,
                    description: '完成JavaScript基础语法相关题目练习'
                },
                {
                    id: 2,
                    name: 'Vue组件化开发实践',
                    status: 2, // 进行中
                    beginTime: '2023-11-05T08:00:00',
                    endTime: '2023-11-15T23:59:59',
                    questionCount: 15,
                    totalScore: 80,
                    submittedCount: 18,
                    unsubmittedCount: 12,
                    pendingReviewCount: 8,
                    description: '完成Vue组件化开发相关实践题目'
                },
                {
                    id: 3,
                    name: '前端性能优化测试',
                    status: 3, // 已完成
                    beginTime: '2023-10-25T08:00:00',
                    endTime: '2023-11-05T23:59:59',
                    questionCount: 10,
                    totalScore: 60,
                    submittedCount: 30,
                    unsubmittedCount: 0,
                    pendingReviewCount: 0,
                    description: '完成前端性能优化相关测试'
                }
            ];
            */
            // 保留原来的API调用作为注释，实际开发时可以取消注释
           
            this.$api.GetStudentTaskList(this.searchParams).then(res => {
                if(res.errCode==0){
                    this.taskList = res.data.items ; // 如果没有数据，使用模拟数据
                }
            })
            
        },
        handleClick(tab, event) {
            console.log(tab, event);
        },
  
        startHomeWork(item) {
          this.$router.push({
            path: '/student/task',
            query: {
              type: 'homework',
              courseId: this.$route.query.courseId,
              taskId: item.id,
              title: item.name,
              status: item.isSubmit?'view':''
            }
          })
        },
        statistics(item) {
            this.$router.push({
                path: '/student/task',
                query: {
                    type: 'homework',
                    courseId: this.$route.query.courseId,
                    taskId: item.id,
                    title: item.name,
                    status:'view'
                }
            })
        },
    }
}
</script>   
  
<style lang="scss" scoped>
.student-home-work-page {
    height: 100%;
    background: #fff;
    padding:20px;
    box-sizing: border-box;
    .top-tool{
       display: flex;
       justify-content: space-between;
       align-items: center;
       padding: 0 0 20px 0;
       background-color: #fff;

       .left{
           display: flex;
           align-items: center;
           justify-content: flex-start;
            .serach-input{
                width: 220px;
                margin-right: 10px;
            }
            .select-status{
                width: 220px;
                margin-right: 10px;
            }
            .search-btn{
                width: 60px;
                height: 38px;
                padding: 0;
                background: #3274FE;
                color: #fff;
                border-radius: 4px;
                border: none;
                &:hover{
                    background: #3274FE;
                }
            }
            .reset-btn{
                width: 60px;
                height: 38px;
                padding: 0;
                color: #5C6075;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                &:hover{
                    background: #D3D3D3;
                }
            }
       }
    }

    .empty-info{
      background-color: #fff;
    }
    .task-list{
       .task-item{
            background-color: #fff;
            border: 1px solid #E7E7E7;
            padding: 20px;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
            overflow: hidden;
            padding-bottom: 14px;
            .task-label{
                position: absolute;
                right: -30px;
                top:22px;
                width: 120px;
                background-color: #07C392;
                transform: rotate(45deg);
                height: 26px;
                line-height: 26px;
                font-size: 14px;
                color: #fff;
                text-align: center;
            }
            .status1{
                background-color: #BBBBBB;
            }
            .status2{
                background-color: #0070FC;
            }
            .status3{
                background-color: #07C392;
            }
           .task-title{
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                margin: 0;
                line-height: 20px;
                margin-bottom:6px;
                .label-1{
                    width: 70px;
                    height: 20px;
                    padding: 0 6px;
                    margin-left:10px;
                    background: #EDF2FF;
                    border-radius: 4px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 13px;
                    color: #0070FC;
                    line-height: 18px;
                    text-align: left;
                    font-style: normal;
                }
                .label-2{
                    width: 70px;
                    height: 20px;
                    padding: 0 6px;
                    margin-left:10px;
                    background: #FFF7EA;
                    border-radius: 4px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 13px;
                    color: #E08E13;
                    line-height: 18px;
                    text-align: left;
                    font-style: normal;
                }
                .label-3{
                    width: 70px;
                    height: 20px;
                    padding: 0 6px;
                    margin-left:10px;
                    background: #E5FCF5;
                    border-radius: 4px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 13px;
                    color: #07C392;
                    line-height: 18px;
                    text-align: left;
                    font-style: normal;
                }



           }
          .task-info{
            height: 90px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #FFF;
            margin-top:18px;

            .left-info{
                width: 520px;
            }

            .info-item{
                text-align: center;
                font-size: 14px;
                display: flex;
                justify-content: flex-start;
                margin-bottom: 10px;
                
                .lab,
                .val{
                    height: 20px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #5C6075;
                    line-height: 20px;
                    text-align: justify;
                    font-style: normal;
                }

                .lab{
                    width: 66px;
                    text-align: justify;
                    text-align-last: justify;
                }
            }
          }

         .task-time{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 24px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            border-top: 1px solid #F2F3F5;
            .remark{
                padding-top:10px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;
            }
           
         }

        .operate-box{
            text-align: right;
            padding-right: 20px;
            .edit-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #0070FC;
                color: #0070FC;
            } 
            .remark-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            } 
            .static-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            }
            .del-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            } 
         }
        }
    }
}
</style>