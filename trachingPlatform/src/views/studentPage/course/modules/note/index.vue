<template>
  <div class="wh-full bg-white flex flex-col rounded-4px p-20px gap-20px">
    <div class="flex items-center">
      <el-input
        v-model="searchText"
        class="search-input w-300px mr-10px"
        placeholder="请搜索名称"
        suffix-icon="el-icon-search"
        clearable
      ></el-input>
      <el-button type="primary" icon="el-icon-plus" class="primary-blue-btn">
        添加分类
      </el-button>
      <el-button type="primary" icon="el-icon-plus" class="primary-green-btn" @click="handleAddNote">
        添加笔记
      </el-button>
      <el-button>
        批量
      </el-button>
    </div>

    <el-table
      :data="tableData"
      style="width: 100%"
      class="b-1px b-solid b-#E7E7E7"
      row-key="id"
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @cell-mouse-enter="onCellMouseEnter"
      @cell-mouse-leave="onCellMouseLeave"
    >
      <el-table-column prop="date" label="名称">
        <template slot-scope="scope">
          <template v-if="scope.row.isEdit">
            <div class="flex items-center">
              <el-input v-model="scope.row.date" class="!w-180px mr-10px" size="small"></el-input>
              <el-button type="primary" class="primary-blue-btn !h-32px" size="small">确认</el-button>
              <el-button size="small">取消</el-button>
            </div>
          </template>
          <template v-else>
            <div
              v-if="scope.row.children"
              class="inline-block text-#333 font-500 text-14px lh-20px ml-10px"
            >
              {{ scope.row.date }}
            </div>
            <div
              v-else
              class="text-#666666 text-14px font-400 inline-block"
              :class="scope?.treeNode ? 'pl-14px' : ''"
            >
              {{ scope.row.date }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="日期" width="200px"> </el-table-column>
      <el-table-column label="操作" width="200px">
        <template slot-scope="scope">
          <div class="row-ops" :class="{ 'row-ops--show': hoverRowKey === scope.row.id || dropdownRowKey === scope.row.id }">
            <el-button type="text" class="text-#333333 text-14px font-400">添加子级</el-button>
            <el-button type="text" class="text-#333333 text-14px font-400">添加笔记</el-button>
            <el-dropdown trigger="click" placement="bottom" @visible-change="onDropdownVisibleChange($event, scope.row)">
              <i class="el-icon-more text-#BBBBBB ml-10px"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>重命名</el-dropdown-item>
                <el-dropdown-item>移动笔记</el-dropdown-item>
                <el-dropdown-item class="text-#F11B1B">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { showNoteEditor } from "@/views/studentPage/course/modules/note/components/editor"

export default {
  name: "CourseNote",
  data() {
    return {
      searchText: "",
      hoverRowKey: null,
      dropdownRowKey: null,
      tableData: [
        {
          id: 1,
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
          isEdit: true,
        },
        {
          id: 2,
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
        {
          id: 3,
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄",
          children: [
            {
              id: 31,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
            {
              id: 32,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
          ],
        },
        {
          id: 4,
          date: "2016-05-03",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1516 弄",
        },
      ],
    }
  },
  methods: {
    // 添加笔记
    handleAddNote() {
      showNoteEditor()
    },

    handleClick(scope) {
      console.log("scope", scope)
    },
    // 行鼠标移入
    onCellMouseEnter(row) {
      this.hoverRowKey = row.id
    },
    // 行鼠标移出
    onCellMouseLeave() {
      if (!this.dropdownRowKey) this.hoverRowKey = null
    },
    // 下拉菜单显示/隐藏
    onDropdownVisibleChange(val, row) {
      const rowKey = row && row.id
      if (val) {
        this.dropdownRowKey = rowKey
      } else {
        if (this.dropdownRowKey === rowKey) {
          this.dropdownRowKey = null
          this.$nextTick(() => {
            if (!this.$el.querySelector('.el-table__row:hover')) {
              this.hoverRowKey = null
            }
          })
        }
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.add-btn {
  width: 120px;
  height: 38px;
  background: #07c392;
  border: 1px solid #07c392;
  color: #fff;
  border-radius: 4px;
  padding: 0;
}

::v-deep .el-table {
  th {
    background-color: #EFF2F5;

    &.el-table__cell > .cell {
      color: #5c6075;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .el-table__expand-icon {
    width: 18px;
    height: 18px;
    background: #edf2ff;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transform: rotate(-90deg) !important;
    transition: none !important;
    vertical-align: middle;
  }

  .el-table__expand-icon--expanded {
    transform: rotate(0deg) !important;
  }

  .el-table__expand-icon > .el-icon-arrow-right:before,
  .el-table__expand-icon.el-icon-arrow-right:before {
    content: "\e608"; /* icon-a-Path2 */
    font-family: "iconfont" !important;
    font-size: 6px;
    color: #0070fc;
    font-style: normal;
    line-height: 18px;
  }
}
.row-ops { opacity: 0; visibility: hidden; pointer-events: none; transition: opacity .15s ease; }
.row-ops.row-ops--show { opacity: 1; visibility: visible; pointer-events: auto; }

</style>
