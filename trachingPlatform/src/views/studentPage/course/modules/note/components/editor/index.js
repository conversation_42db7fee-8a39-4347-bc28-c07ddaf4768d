import NoteEditor from './index.vue'
import Vue from "vue"

export const showNoteEditor = (options) => {
  return new Promise((resolve, reject) => {
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 清理函数
    const cleanup = () => {
      if (container && container.parentNode) {
        document.body.removeChild(container)
      }
    }

    const noteEditor = new Vue({
      render: h => h(NoteEditor, {
        props: {
          ...options
        },
        on: {
          save: (val) => {
            resolve(val)
            noteEditor.$destroy()
            cleanup()
          },
          back: () => {
            // 添加延迟以确保动画完成后再销毁组件
            setTimeout(() => {
              reject()
              noteEditor.$destroy()
              cleanup()
            }, 350) // 等待动画完成（300ms + 50ms缓冲）
          }
        }
      })
    }).$mount(container)
  })
}
