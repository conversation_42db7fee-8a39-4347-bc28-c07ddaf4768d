<template>
  <transition
    enter-active-class="animate-slide-in-up animate-duration-300"
    leave-active-class="animate-slide-out-down animate-duration-300"
    @after-leave="onAfterLeave"
  >
    <div v-show="visible" class="note-editor animate-slide-in-up animate-duration-300">
      <div
        class="flex items-center justify-between px-20px h-60px note-editor__header bg-white flex-shrink-0"
      >
        <div class="flex items-center">
          <div class="el-icon-arrow-left font-bold" @click="handleBack"></div>
        </div>
        <el-button
          type="primary"
          class="primary-blue-btn !h-38px"
          size="small"
          @click="handleSave"
        >保存</el-button
        >
      </div>
      <div class="flex flex-col h-full flex-1-hidden">
        <div class="h-42px flex-shrink-0 bg-white b-1px b-solid b-#e8e8e8 b-l-none b-r-none">
          <Toolbar
            :editor="editor"
            :defaultConfig="toolbarConfig"
            :mode="mode"
          />
        </div>
        <div class="content">
          <div class="editor-container">
            <div class="title-container">
              <input v-model="noteTitle" placeholder="请输入标题" />
            </div>
            <div class="editor-wrapper">
              <Editor
                v-model="html"
                :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="onCreated"
                @onChange="onChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue"

export default {
  name: "NoteEditor",
  components: {
    Editor,
    Toolbar,
  },
  data() {
    return {
      editor: null,
      html: "",
      noteTitle: "",
      mode: "default", // 或 'simple'
      visible: true, // 控制组件显示/隐藏
    }
  },
  computed: {
    toolbarConfig() {
      return {
        excludeKeys: ["fullScreen"],
      }
    },
    editorConfig() {
      return {
        placeholder: "请输入内容...",
        scroll: false, // 禁止编辑器滚动
        MENU_CONF: {
          uploadImage: {
            fieldName: "your-fileName",
            base64LimitSize: 10 * 1024 * 1024, // 10M 以下插入 base64
          },
        },
      }
    },
  },
  mounted() {
    this.setPageEvent()
  },
  beforeDestroy() {
    // 组件销毁时，也及时销毁编辑器
    if (this.editor == null) return
    this.editor.destroy()
    this.editor = null
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      console.log("editor created", editor)
    },
    onChange(editor) {
      console.log("editor change", editor.getHtml())
      // 这里可以处理内容变化
    },
    // 保存笔记
    handleSave() {
      if (this.editor) {
        const content = this.editor.getHtml()
        console.log("保存内容:", { title: this.noteTitle, content })
        // 这里添加保存逻辑
        this.$message.success("笔记保存成功")
      }
    },
    // 返回
    handleBack() {
      // 先触发退出动画
      this.visible = false
    },
    // 动画结束后的回调
    onAfterLeave() {
      // 动画完成后发出back事件
      this.$emit('back')
    },
    // 监听页面
    setPageEvent() {
      const handleBeforeUnload = (event) => {
        event.preventDefault()
        event.returnValue = ""
      }
      window.addEventListener("beforeunload", handleBeforeUnload)

      this.$once("hook:beforeDestroy", () => {
        window.removeEventListener("beforeunload", handleBeforeUnload)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.note-editor {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  z-index: 9999;
  overflow: hidden;
  position: fixed;
  left: 0;
  top: 0;
  background-color: #f7f7f7;

  &__header {
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.1);
  }

  :deep(.w-e-toolbar) {
    width: 1350px;
    background-color: #ffffff;
    margin: 0 auto;
    border: none !important;
  }

  .content {
    height: calc(100% - 40px);
    background-color: rgb(245, 245, 245);
    overflow-y: auto;
    position: relative;
  }

  .editor-container {
    width: 850px;
    margin: 30px auto 150px auto;
    background-color: #fff;
    padding: 20px 50px 50px 50px;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
  }

  .title-container {
    padding: 20px 0;
    border-bottom: 1px solid #e8e8e8;
  }

  .title-container input {
    font-size: 30px;
    border: 0;
    outline: none;
    width: 100%;
    line-height: 1;
  }

  .editor-wrapper {
    min-height: 900px;
    margin-top: 20px;
  }

  :deep(.w-e-text-container) {
    min-height: 900px !important;
  }

  :deep(.w-e-text-placeholder) {
    color: #999;
  }
}
</style>
