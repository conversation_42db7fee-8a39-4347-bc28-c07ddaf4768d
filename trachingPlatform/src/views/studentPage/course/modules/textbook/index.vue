<template>
  <div class="wh-full bg-#fff p-20px flex flex-col gap-20px">
    <div class="flex justify-end">
      <el-input v-model="searchText" class="w-460px" placeholder="请输入教材名称">
        <el-button
          slot="append"
          class="!text-#fff primary-blue-btn !rounded-tl-0 !rounded-bl-0"
          type="primary"
          clearable
          @click="handleSearch"
        >搜索</el-button>
      </el-input>
    </div>
    <div class="flex-1-hidden">
      <el-scrollbar
        v-if="!loading && textbooks.length"
        class="h-full flex flex-col gap-20px"
      >
        <div
          v-for="item in textbooks"
          :key="item.id"
          class="flex items-start gap-20px py-20px px-24px b-1px b-solid b-#e7e7e7 rounded-4px cursor-pointer"
          @click="handleToTextbook(item)"
        >
          <img
            class="w-122px h-162px object-cover rounded-4px b-1px b-solid b-#e7e7e7 rounded-4px"
            :src="item.coverImage"
            :alt="item.name"
          />
          <div class="flex-1-hidden flex flex-col">
            <div class="text-16px font-500 text-#333 pt-10px py-20px">{{ item.name }}</div>
            <div class="flex items-center gap-20px font-400 text-14px text-#5c6075">
              <span>主编：{{ item.publishedChiefEditor || '-' }}</span>
              <el-divider direction="vertical" />
              <span>出版社：{{ item.publishingHouse || '-' }}</span>
              <el-divider direction="vertical" />
              <span>书号：{{ item.publishedBookNumber || '-' }}</span>
            </div>
            <div v-if="item.description" class="font-400 text-14px text-#333 pt-14px">
              {{ item.description || '-' }}
            </div>
          </div>
        </div>
      </el-scrollbar>

      <div v-else class="wh-full flex items-center justify-center">
        <Empty type="noData" :loading="loading" msg="暂无教材" size="middle" />
      </div>
    </div>
  </div>
</template>

<script>
import Empty from "@/components/base/empty.vue"

export default {
  components: { Empty },
  data() {
    return {
      loading: false,
      searchText: null,
      textbooks: [],
      originalTextbooks: []
    }
  },
  created() {
    this.getTextbookList()
  },
  methods: {
    // 获取教材列表
    async getTextbookList() {
      try {
        this.loading = true
        const res = await this.$api.GetCourseDetail({
          id: this.$route.query.courseId,
        })
        this.originalTextbooks = res.data?.textbooks ?? []
        this.textbooks = this.originalTextbooks
      } finally {
        this.loading = false
      }
    },
    // 搜索
    handleSearch() {
      this.textbooks = this.originalTextbooks.filter((item) => {
        return item.name.includes(this.searchText)
      })
    },
    // 跳转到教材 - 笔记
    handleToTextbook(item) {
      const routerData = this.$router.resolve({ path: `/student/editor/note`, query: { id: item.id } });
      window.open(routerData.href, '_blank');
    },
  },
}
</script>

<style lang="scss"></style>
