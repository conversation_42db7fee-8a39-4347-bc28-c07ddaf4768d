<template>
    <div class="student-exam-page">
        <div class="top-tool">
            <div class="left">
                <el-input class="serach-input" clearable @change="initList()" v-model="searchParams.Name" placeholder="请输入任务名称" suffix-icon="iconfont icon-sousuo"></el-input>
                <el-select class="select-status" clearable @change="initList()" v-model="searchParams.Status" placeholder="请选择作业状态">
                    <el-option v-for="item in statusList" :key="item.id" :label="item.name" :value="item.value"></el-option>
                </el-select>
                <el-button class="search-btn" type="primary">搜索</el-button>
                <el-button class="reset-btn" type="default" @click="resetSearchParams">重置</el-button>
            </div>
        </div>
        <div class="task-list">
             <!-- 使用 Element UI 栅格系统 -->
             <el-row :gutter="20" v-if="taskList.length>0">
                <el-col :span="12" v-for="item in taskList" :key="item.id">
                    <div class="task-item">
                        <p :class="getTaskStatusClass(item.status)">{{getTaskStatusText(item.status)}}</p>
                        <h3 class="task-title">{{item.name}}</h3>
                        <div class="task-info">
                            <div class="info-item">
                                <p class="val">{{item.totalScore}}</p>
                                <p class="lab">卷面分</p> 
                            </div>
                            <div class="info-item">
                                <p class="val">{{item.totalScore}}</p>
                                <p class="lab">得分</p> 
                            </div>
                           
                        </div>
                        <p class="task-time">
                            <span class="time">时间：{{formatISOString(item.beginTime)}}~{{formatISOString(item.endTime)}}</span>
                            <span class="remark">备注：{{item.description}}</span>
                        </p>
                        <div class="operate-box">
                            <el-button v-if="item.status=='InProgress'" class="remark-btn" @click="startExam(item)">立即作答</el-button>
                            <el-button v-if="item.status=='Finished'" class="static-btn" @click="statistics(item)">作答卷面</el-button>
                        </div>
                    </div>
                </el-col>
            </el-row>
            <empty style="margin-top:100px;"  v-if="taskList.length==0" msg="暂无数据" size="middle" />
        </div>
    </div>
</template>
  
<script>
    import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
    export default {
        name: 'student-exam-page',
        data() {
            return {
                taskName: '',
                taskStatus: '',
                statusList:[
                    {id:1,name:'未开始',value:'NotStarted'},
                    {id:2,name:'进行中',vallue:'InProgress'},
                    {id:3,name:'已完成',value:'Finished'}
                ],
                taskList: [], // 任务列表
                searchParams: { // 搜索参数
                    CourseId: this.$route.query.courseId, // 课程ID
                    Name: '', // 任务名称
                    TaskCategory:'考试', // 任务类型 作业||考试
                    TagType:'', // 考试类型 课前预习, 课中授课, 课后巩固
                    Status: '', // 任务状态
                    PageIndex: 1, // 页码
                    PageSize: 10 // 每页数量
                }
            } 
        },
        components: {
            // 引入组件
            empty:()=>import("@/components/base/empty.vue"),
        },
        mounted() {
            this.initList(); // 初始化任务列表
        },
        methods: {
            formatISOString,
            // 获取任务状态对应的CSS类名
            getTaskStatusClass(status) {
                const baseClass = 'task-label';
                let statusClass = '';
                switch(status) {
                    case 1:
                        statusClass = 'status1';
                        break;
                    case 2:
                        statusClass = 'status2';
                        break;
                    case 3:
                        statusClass = 'status3';
                        break;
                }
                return [baseClass, statusClass].join(' ');
            },
            // 获取任务状态对应的文本
            getTaskStatusText(status) {
                switch(status) {
                    case 'NotStarted':
                        return '未开始';
                    case 'InProgress':
                        return '进行中';
                    case 'Finished':
                        return '已完成';
                    default:
                        return '';
                }
            },
            initList() {
                // 初始化任务列表
                // 模拟数据，用于测试显示
                /*
                this.taskList = [
                    {
                        id: 1,
                        name: '前端开发基础测试',
                        status: 1, // 未开始
                        totalScore: 100,
                        beginTime: '2023-11-15T09:00:00',
                        endTime: '2023-11-15T11:00:00',
                        description: '包含HTML、CSS、JavaScript基础知识点'
                    },
                    {
                        id: 2,
                        name: 'Vue框架实战考试',
                        status: 2, // 进行中
                        totalScore: 120,
                        beginTime: '2023-11-10T14:00:00',
                        endTime: '2023-11-10T16:30:00',
                        description: 'Vue组件化开发、状态管理、路由等核心知识点'
                    },
                    {
                        id: 3,
                        name: 'React高级应用测试',
                        status: 2, // 进行中
                        totalScore: 150,
                        beginTime: '2023-11-08T10:00:00',
                        endTime: '2023-11-08T12:30:00',
                        description: 'React Hooks、性能优化、Redux等高级知识点'
                    },
                    {
                        id: 4,
                        name: '前端性能优化测试',
                        status: 3, // 已完成
                        totalScore: 80,
                        beginTime: '2023-11-01T15:00:00',
                        endTime: '2023-11-01T16:30:00',
                        description: '包含网络优化、资源加载优化、渲染优化等内容'
                    }
                ];
                 */
                // 保留原来的API调用作为注释，实际开发时可以取消注释
                
                this.$api.GetStudentTaskList(this.searchParams).then(res => {
                    if(res.errCode==0){
                        this.taskList = res.data.items || []; // 如果没有数据，使用模拟数据
                    }
                })
               
            },
            handleClick(tab, event) {
                console.log(tab, event);
            },
            statistics(item) {
                this.$router.push({
                    path: '/student/task',
                    query: {
                        type: 'exam',
                        courseId: this.$route.query.courseId,
                        taskId: item.id,
                        title: item.name,
                        status:'view',
                    }
                })
            },
            startExam(item) {
                this.$router.push({
                    path: '/student/task',
                    query: {
                        type: 'exam',
                        courseId: this.$route.query.courseId,
                        taskId: item.id,
                        title: item.name,
                    }
                })
            },
        }
    }
</script>   
  
<style lang="scss" scoped>
.student-exam-page {
    height: 100%;
    background-color: #fff;
    padding: 20px;
    box-sizing: border-box;
    .top-tool{
       display: flex;
       justify-content: space-between;
       align-items: center;
       padding: 0 0 20px 0;
       background-color: #fff;

       .left{
           display: flex;
           align-items: center;
           justify-content: flex-start;
            .serach-input{
                width: 220px;
                margin-right: 10px;
            }
            .select-status{
                width: 220px;
                margin-right: 10px;
            }
            
            .search-btn{
                width: 60px;
                height: 38px;
                padding: 0;
                background: #3274FE;
                color: #fff;
                border-radius: 4px;
                border: none;
                &:hover{
                    background: #3274FE;
                }
            }
            .reset-btn{
                width: 60px;
                height: 38px;
                padding: 0;
                color: #5C6075;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                &:hover{
                    background: #D3D3D3;
                }
            }
       }
    }

    .empty-info{
      background: #fff;
    }
    .task-list{
        // margin-top:10px;
       /* background-color: #F6F8FA; */

       .task-item{
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid #E7E7E7;
            .task-label{
                position: absolute;
                right: -30px;
                top:22px;
                width: 120px;
                background-color: #07C392;
                transform: rotate(45deg);
                height: 26px;
                line-height: 26px;
                font-size: 14px;
                color: #fff;
                text-align: center;
            }
            .status1{
                background-color: #BBBBBB;
            }
            .status2{
                background-color: #0070FC;
            }
            .status3{
                background-color: #07C392;
            }
           .task-title{
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                margin: 0;
                line-height: 20px;
                margin-bottom:16px;
           }
          .task-info{
            height: 80px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: #F6FAFF;
            border-radius: 4px;
            .info-item{
                width: 64px;
                text-align: center;
                font-size: 14px;
                .val{
                    font-size: 18px;
                    color: #333;
                }
               .lab{
                   font-size: 14px;
                   color: #5C6075;
               }
            }
          }

         .task-time{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 24px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            border-bottom: 1px solid #F2F3F5;
            .time,
            .remark{
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 50%;
            }
           
         }

        .operate-box{
            margin-top: 20px;
            text-align: right;
            .edit-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #0070FC;
                color: #0070FC;
            } 
            .remark-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            } 
            .static-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            }
            .del-btn{
                width: 100px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                color: #333333;
            } 
         }
        }
    }
}
</style>