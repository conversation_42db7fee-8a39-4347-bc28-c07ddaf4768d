<template>
  <div class="w-full h-full flex flex-col">
    <div class="w-full bg-white h-50px rounded-4px px-20px">
      <el-tabs v-model="activeName" class="h-full">
        <el-tab-pane label="课程信息" name="first"></el-tab-pane>
        <el-tab-pane label="课程评价" name="second"></el-tab-pane>
      </el-tabs>
    </div>
    <div
      v-if="activeName === 'first'"
      v-loading="loading"
      class="w-full flex-1-hidden bg-#fff flex flex-col item-center justify-center"
    >
      <div class="flex space-x-20px m-a">
        <img
          :src="courseInfo.coverImage"
          alt=""
          class="w-284px h-160px object-cover rounded-4px"
        />
        <div class="flex flex-col">
          <div class="text-16px font-500 text-#333 pb-20px">
            {{ courseInfo.name }}
          </div>
          <div class="flex flex-col gap-14px">
            <div class="flex item-center gap-40px">
              <div>
                <span
                  class="label"
                >学&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：</span
                ><span class="value">{{ courseInfo.hours }}</span>
              </div>
              <div>
                <span class="label">专业层次：</span
                ><span class="value">{{ courseInfo.majorCengCi }}</span>
              </div>
            </div>
            <div>
              <span class="label">所属专业：</span
              ><span class="value">{{ courseInfo.majorCategory }}</span>
            </div>
            <div class="w-360px">
              <span class="label">课程简介：</span
              ><span class="value">{{ courseInfo.description }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="activeName === 'second'"
      class="w-full flex-1 bg-#fff py-24px px-26px overflow-y-auto"
    >
      <div v-if="isCourseCommentEdit">
        <el-form
          ref="courseCommentFormRef"
          label-width="60px"
          label-position="left"
          :model="courseComment"
          :rules="courseCommentRules"
        >
          <el-form-item
            label="评分"
            required
            class="flex items-center pb-8px b-b-1px b-b-#F2F3F5 b-b-solid"
          >
            <div class="flex items-center gap-20px">
              <div class="text-#F11B1B text-30px">
                {{ (courseComment.score || 0).toFixed(1) }}
              </div>
              <el-rate
                v-model="courseComment.score"
                :colors="['#F11B1B', '#F11B1B', '#F11B1B']"
                text-color="#F11B1B"
                class="!text-20px"
              />
            </div>
          </el-form-item>
          <el-form-item
            label="评语"
            prop="content"
            class="pt-20px"
            lable-width="100%"
          >
            <el-input
              v-model="courseComment.content"
              type="textarea"
              placeholder="说说课程的亮点、特色和不足吧~"
              show-word-limit
              :maxlength="200"
              :rows="6"
              class="pt-20px"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="flex justify-end mt-40px">
          <el-button
            type="primary"
            :loading="courseCommentLoading"
            class="primary-green-btn"
            @click="handleSubmitCourseComment"
          >
            提交
          </el-button>
        </div>
      </div>
      <div
        v-else
        class="flex flex-col gap-12px pb-20px b-b-1px b-b-#F2F3F5 b-b-solid"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-20px">
            <div class="text-#F11B1B text-30px">
              {{ (courseComment.score || 0).toFixed(1) }}
            </div>
            <el-rate
              v-model="courseComment.score"
              :colors="['#F11B1B', '#F11B1B', '#F11B1B']"
              text-color="#F11B1B"
              class="!text-20px"
              disabled
            />
          </div>
          <div class="flex items-center">
            <el-button
              type="text"
              icon="iconfont icon-bianji1"
              @click="handleCourseCommentEdit"
            ></el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              class="text-#ff2828 text-14px hover:text-#ff2828 active:text-#ff2828"
              @click="handleCourseCommentDelete"
            ></el-button>
          </div>
        </div>
        <div class="text-#333 text-14px lh-20px font-400 comment-content">
          {{ courseCommentContent }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CourseInfo",
  data() {
    return {
      loading: false,
      courseCommentLoading: false,
      activeName: "first",
      courseInfo: {}, // 课程信息
      isCourseCommentEdit: true,
      courseComment: {}, // 课程评价
      courseCommentRules: {
        content: [{ required: true, message: "请输入评语", trigger: "blur" }],
      },
    }
  },
  computed: {
    courseCommentContent() {
      return (this.courseComment.content || '').replace(/\r?\n/g, '\n')
    },
  },
  created() {
    this.getCourseInfo()
    this.getCourseComment()
  },
  methods: {
    // 获取课程信息
    async getCourseInfo() {
      try {
        this.loading = true
        const res = await this.$api.GetCourseDetail({
          id: this.$route.query.courseId,
        })
        this.courseInfo = res.data || {}
      } finally {
        setTimeout(() => {
          this.loading = false
        }, 300)
      }
    },
    // 获取课程评价
    async getCourseComment() {
      const res = await this.$api.StudentGetMyCourseComment({
        courseId: this.$route.query.courseId,
      })
      this.courseComment = res.data || {
        score: 5,
      }
      this.isCourseCommentEdit = !res.data
    },
    // 提交课程评价
    async handleSubmitCourseComment() {
      const valid = await this.$refs.courseCommentFormRef.validate()
      console.log("valid", valid)
      if (!valid) return
      try {
        this.courseCommentLoading = true
        const res = await this.$api.StudentCourseCommentManage({
          ...this.courseComment,
          courseId: this.$route.query.courseId,
        })
        if (!res.errCode) {
          this.$message.success("感谢您的评价!")
          await this.getCourseComment()
        }
      } finally {
        this.courseCommentLoading = false
      }
    },
    // 编辑评价
    async handleCourseCommentEdit() {
      this.isCourseCommentEdit = true
    },
    // 删除评价
    async handleCourseCommentDelete() {
      await this.$zdDialog({
        contImg: "",
        contTitle: "确定删除?",
        contDesc: "删除后可重新评价",
      })
      const res = await this.$api.StudentCourseCommentDelete({
        id: this.courseComment.id,
      })
      if (res.data) {
        this.$message.success("删除成功")
        this.isCourseCommentEdit = true
        this.courseComment = {
          score: 5,
        }
      } else {
        this.$message.error("删除失败")
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__nav-wrap {
  &::after {
    display: none;
  }
}

::v-deep .el-tabs__item {
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 400;
  color: #5c6075;
  &.is-active {
    color: #0070fc;
  }
}

.label {
  font-weight: 400;
  font-size: 14px;
  color: #999;
  line-height: 20px;
}

.value {
  font-weight: 400;
  font-size: 14px;
  color: #333;
  line-height: 20px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  font-size: 16px;
  color: #333;
  line-height: 22px;
}

::v-deep .el-form-item__content {
  margin-left: 0 !important;
}
.comment-content {
  white-space: pre-line;
}

</style>
