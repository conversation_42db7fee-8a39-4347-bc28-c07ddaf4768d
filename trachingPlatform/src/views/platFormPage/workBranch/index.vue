<template>
    <div class="plant-branch-page">
        <Notice />
        <div class="content">
            <div class="title">
                <p><i class="iconfont icon-jianzhu06-F"></i>管理统计 <span>全平台管理相关工作数据统计</span> </p>
            </div>
            <div class="base-data">
                <h4 class="part-label">基础数据</h4>
                <div class="data-box">
                    <div class="data-item" v-for="(item,index) in baseData1" :key="index">
                        <div class="data-label">
                            {{item.label}}
                            <el-tooltip class="item" :content="item.tip" placement="right">
                                <i v-if="item.tip" style="color:#DEDEDE;" class="iconfont icon-wenhao"></i>
                            </el-tooltip>
                        </div>
                        <div class="data-value">{{item.value}}</div>
                    </div>
                    <div class="data-chart">
                        <div id="pieEcharts" style="width:342px;height:102px;"></div>
                    </div>
                </div>
                <div class="data-box" style="margin:20px 0;border:none">
                    <div class="data-item" v-for="( item,index) in baseData2" :key="index">
                        <div class="data-label">
                            {{item.label}}
                            <el-tooltip class="item" :content="item.tip" placement="right">
                                <i style="color:#DEDEDE;" class="iconfont icon-wenhao"></i>
                            </el-tooltip>
                        </div>
                        <div class="data-value">{{item.value}}</div>
                    </div>
                </div>
            </div>
            <div class="data-static">
                <div class="textBook-data">
                    <h4 class="part-label">教材排行榜</h4>
                    <div id="bar1" style="width:100%;height:340px;">
                    </div>
                </div>
                <div class="user-data">
                    <h4 class="part-label">学校用户数排行榜</h4>
                    <div id="bar2" style="width:100%;height:340px;">
                    </div>
                </div>  
            </div>
            <div class="data-static">
                 <div class="login-data">
                    <h4 class="part-label">登录次数排行榜</h4>
                    <div style="width:100%;height:340px;margin-top:20px;">
                        <el-table
                            :data="logoRecodeData"
                            border
                            :header-cell-style="{ background: '#F2F3F5', color: '#222222' }"
                            style="width: 100%">
                            <el-table-column
                             type="index"
                            label="序号"
                            align="center"
                            width="60">
                            </el-table-column>
                            <el-table-column
                            prop="schoolName"
                            label="学校">
                            </el-table-column>
                            <el-table-column
                            prop="teacherNum"
                            label="教师登录次数"    
                            width="120">
                            </el-table-column>
                            <el-table-column
                            prop="studentNum"
                            label="学生登录次数"
                            width="120">
                            </el-table-column>
                            <el-table-column
                            prop="totalLoginNum"
                            label="累计登录次数"
                            width="120">
                            </el-table-column>
                            <el-table-column
                            prop="rank"
                            label="排名"
                            width="100"
                            >
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="book-data">
                    <h4 class="part-label">教材使用情况</h4>
                    <div style="width:100%;height:340px;margin-top:20px;">
                         <el-table
                            :data="textBookData"
                            border
                            :header-cell-style="{ background: '#F2F3F5', color: '#222222' }"
                            style="width: 100%">
                            <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="60">
                            </el-table-column>
                            <el-table-column
                            prop="schoolName"
                            label="学校">
                            </el-table-column>
                            <el-table-column
                            prop="教材数量"
                            width="100"
                            align="center"
                            label="教材数">
                            </el-table-column>
                            <el-table-column
                            prop="教材使用率"
                            width="100"
                            align="center"
                            label="使用率">
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            pageData:{},// 界面统计
            statisticsList:[],
            baseData1:[
                {
                    label: '合作学校',
                    value: 1560,
                    tip: '提示文字'
                },
                {
                    label: '中职学校',
                    value: 780,
                    tip: ''
                },
                {
                    label: '高职学校',
                    value: 520,
                    tip: ''
                },
                {
                    label: '本科学校',
                    value: 260,
                    tip: ''
                },
                {
                    label: '近七日新增院校',
                    value: 86,
                    tip: '' 
                },
                {
                    label: '创建学院数',
                    value: 46,
                    tip: '提示文字'
                },
            ],
            baseData2:[
                {
                    label: '教师数量',
                    value: 486,
                    tip: '提示文字' 
                }, 
                {
                    label: '学生数量',
                    value: 5860,
                    tip: '提示文字'
                },
                {
                    label: '班级数量',
                    value: 60,
                    tip: '提示文字'
                },
                {
                    label: '累计申请教材额度',
                    value: 586,
                    tip: '提示文字'
                },
                {
                    label: '累计已使用额度',
                    value: 68923,
                    tip: '提示文字'
                },
                {
                    label: '累计创建课程数量',
                    value: 1260,
                    tip: '提示文字'
                },
                {
                    label: '累计已上架未出版教材',
                    value: 169,
                    tip: '提示文字' 
                },
                {
                    label: '累计添加已出版教材',
                    value: 169,
                    tip: '提示文字' 
                }
            ],
            chartPie:null,
            pieOption: {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    top: '6%',
                    left: 'right',
                    orient: 'vertical', // 设置图例垂直排列
                    textStyle: {
                        fontSize: 12, // 设置图例文字大小
                    },
                    itemWidth: 10, // 设置图例标记的宽度
                    itemHeight: 10, // 设置图例标记的高度
                    align: 'left' // 设置文字在右，色块在左
                },
                series: [
                    {
                    name: '学校统计',
                    type: 'pie',
                    radius: ['70%', '45%'], // 调整环形比例，使其更适合小尺寸容器
                    avoidLabelOverlap: false,
                    // 修改标签配置，让标签显示在右侧
                    label: {
                        show: true,
                        position: 'right',
                        formatter: function(params) {
                            // 使用富文本标签设置与数据项相同的颜色
                            return '{a|' + params.name + ' ' + params.percent + '%}';
                        },
                        // 设置富文本样式，使标签颜色与数据项颜色保持一致
                        rich: {
                            a: {
                                color: function(params) {
                                    // 返回当前数据项的颜色
                                    return params.color;
                                },
                                fontSize: 12,
                                fontWeight: 'normal'
                            }
                        }
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 14,
                            fontWeight: 'bold'
                        }
                    },
                    // 调整标签线配置，确保标签和饼图之间有连线
                    labelLine: {
                        show: true,
                        length: 5, // 缩短第一段线长度
                        length2: 10, // 缩短第二段线长度
                        // lineStyle: {
                        //     color: '#999',
                        //     width: 2
                        // }
                    },
                    // 调整数据，使其与截图效果一致
                    data: [
                        { value: 30, name: '中职',itemStyle: { color: '#FFB573' } },
                        { value: 280, name: '高职',itemStyle: { color: '#759FF8' } },
                        { value: 90, name: '本科',itemStyle: { color: '#6FDEB4' } },
                    ]
                    }
                ]  
            },
            chartBar1:null,
            bar1Option: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                    }
                },
              
                legend: {
                    data: ['申请额度', '使用额度'],
                    left: 'right', // 设置图例水平居中
                    top: '5%', // 设置图例距离顶部 5%
                    textStyle: {
                        color: '#666' // 设置图例文字颜色
                    },
                    itemWidth: 12,
                    itemHeight: 12,
                    // 可以单独为每个图例项设置颜色
                    pageIconColor: '#409EFF', // 分页图标颜色
                    pageTextStyle: {
                        color: '#666' // 分页文字颜色
                    },
                    // 图例标记颜色，可通过修改 series 里的 itemStyle 来控制，这里模拟设置
                    // 如果要单独设置每个图例标记颜色，可结合 series 数据
                    // 下面这种方式在实际使用中可以结合 series 对应数据
                    data: [
                        {
                            name: '申请额度',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#759FF8' // 单独设置 '申请额度' 文字颜色
                            }
                        },
                        {
                            name: '使用额度',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#6FDEB4' // 单独设置 '使用额度' 文字颜色
                            }
                        }
                    ]
                },
                xAxis: [
                    {
                        type: 'category',
                        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        axisPointer: {
                            type: 'shadow'
                        }
                    }
                ],
                yAxis: [
                    {
                    type: 'value',
                    name: '额度',
                    min: 0,
                    interval: 100,
                    axisLabel: {
                        formatter: '{value} '
                    }
                    },
                ],
                series: [
                    {
                    name: '申请额度',
                    type: 'bar',
                    barWidth: '10px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                     // 设置申请额度柱子颜色
                     itemStyle: {
                        color: '#759FF8'
                    },
                    data: [
                        2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                    ]
                    },
                    {
                    name: '使用额度',
                    type: 'bar',
                    barWidth: '10px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                     // 设置使用额度柱子颜色
                     itemStyle: {
                        color: '#6FDEB4'
                    },
                    data: [
                        2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                    ]
                    },
                ]
            },
            chartBar2:null,
            bar2Option: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                    }
                },
                legend: {
                    data: ['教师', '学生'],
                    left: 'right', // 设置图例水平居中
                    top: '5%', // 设置图例距离顶部 5%
                    textStyle: {
                        color: '#666' // 设置图例文字颜色
                    },
                    itemWidth: 12,
                    itemHeight: 12,
                    // 可以单独为每个图例项设置颜色
                    pageIconColor: '#409EFF', // 分页图标颜色
                    pageTextStyle: {
                        color: '#666' // 分页文字颜色
                    },
                    // 图例标记颜色，可通过修改 series 里的 itemStyle 来控制，这里模拟设置
                    // 如果要单独设置每个图例标记颜色，可结合 series 数据
                    // 下面这种方式在实际使用中可以结合 series 对应数据
                    data: [
                        {
                            name: '教师',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#FFB573' // 单独设置 '申请额度' 文字颜色
                            }
                        },
                        {
                            name: '学生',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#DA6FDC' // 单独设置 '使用额度' 文字颜色
                            }
                        }
                    ]
                },
                xAxis: [
                    {
                    type: 'category',
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    axisPointer: {
                        type: 'shadow'
                    }
                    }
                ],
                yAxis: [
                    {
                    type: 'value',
                    name: '数量',
                    min: 0,
                    interval: 50,
                    axisLabel: {
                        formatter: '{value} '
                    }
                    },
                ],
                series: [
                    {
                    name: '教师',
                    type: 'bar',
                    barWidth: '10px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                    // 设置申请额度柱子颜色
                    itemStyle: {
                        color: '#FFB573'
                    },
                    data: [
                        2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                    ]
                    },
                    {
                    name: '学生',
                    type: 'bar',
                    barWidth: '10px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                    // 设置使用额度柱子颜色
                    itemStyle: {
                        color: '#DA6FDC'
                    },
                    data: [
                        2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                    ]
                    },
                ]
            },
            logoRecodeData:[
                {
                    schoolName: '北京大学',
                    teacherNum: 1256,
                    studentNum: 18763,
                    totalLoginNum: 20019,
                    rank: 1
                },
                {
                    schoolName: '清华大学',
                    teacherNum: 1124,
                    studentNum: 17521,
                    totalLoginNum: 18645,
                    rank: 2
                },
                {
                    schoolName: '复旦大学',
                    teacherNum: 987,
                    studentNum: 15324,
                    totalLoginNum: 16311,
                    rank: 3
                },
                {
                    schoolName: '上海交通大学',
                    teacherNum: 923,
                    studentNum: 14876,
                    totalLoginNum: 15800,
                    rank: 4
                },
                {
                    schoolName: '浙江大学',
                    teacherNum: 876,
                    studentNum: 14235,
                    totalLoginNum: 15111,
                    rank: 5
                }
            ],
            textBookData:[// 教材
                {
                    schoolName: '北京大学',
                    '教材数量': 125,
                    '教材使用率': '89%'
                },
                {
                    schoolName: '清华大学',
                    '教材数量': 118,
                    '教材使用率': '85%'
                },
                {
                    schoolName: '复旦大学',
                    '教材数量': 96,
                    '教材使用率': '78%'
                },
                {
                    schoolName: '上海交通大学',
                    '教材数量': 102,
                    '教材使用率': '82%'
                },
                {
                    schoolName: '浙江大学',
                    '教材数量': 93,
                    '教材使用率': '76%'
                }
            ]
        };
    },
    components: {
       Notice: ()=>import('./components/notice.vue'),
    },
    mounted() {
      
        this.initPageData();
        // 监听窗口大小变化，动态调整图表宽度
        window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
        // 组件销毁前移除事件监听
        window.removeEventListener('resize', this.handleResize);
    },
    methods: {
        /**
         * 初始化饼状图
         */
        initBar() {
            this.chartPie = echarts.init(document.getElementById("pieEcharts"));
            this.chartPie.setOption(this.pieOption);

            this.chartBar1 = echarts.init(document.getElementById("bar1"));
            this.chartBar1.setOption(this.bar1Option);

            this.chartBar2 = echarts.init(document.getElementById("bar2"));
            this.chartBar2.setOption(this.bar2Option);
        },
        
        /**
         * 处理窗口大小变化，调整图表宽度
         */
        handleResize() {
            if (this.chartPie) {
                this.chartPie.resize();
            }
            if (this.chartBar1) {
                this.chartBar1.resize();
            }
            if (this.chartBar2) {
                this.chartBar2.resize();
            }
        },
        initPageData() {
            this.$api.PlatformBasicStatistic({}).then(res => {
                if (res.errCode == 0) {
                    this.pageData = res.data;
                    this.baseData1 = [
                        {
                            label: '合作学校',
                            value: this.pageData['学校总数'],
                            tip: '提示文字'
                        },
                        {
                            label: '中职学校',
                            value: this.pageData['中职数量'],
                            tip: ''
                        },
                        {
                            label: '高职学校',
                            value: this.pageData['高职数量'],
                            tip: ''
                        },
                        {
                            label: '本科学校',
                            value: this.pageData['本科数量'],
                            tip: ''
                        },
                        {
                            label: '近七日新增院校',
                            value: this.pageData['近7天新增学校数量'],
                            tip: '' 
                        },
                        {
                            label: '创建学院数',
                            value: this.pageData['创建学院数'],
                            tip: '提示文字'
                        },
                    ]
                    this.baseData2 = [
                        {
                            label: '教师数量',
                            value: this.pageData['教师总数量'],
                            tip: '提示文字' 
                        }, 
                        {
                            label: '学生数量',
                            value: this.pageData['学生总数量'],
                            tip: '提示文字'
                        },
                        {
                            label: '班级数量',
                            value: this.pageData['班级总数量'], 
                            tip: '提示文字'
                        },
                        {
                            label: '累计申请教材额度',
                            value: this.pageData['累计申请教材额度'],
                            tip: '提示文字'
                        },
                        {
                            label: '累计已使用额度',
                            value: this.pageData['累计已使用教材额度'],
                            tip: '提示文字'
                        },
                        {
                            label: '累计创建课程数量',
                            value: this.pageData['累计创建课程数量'],
                            tip: '提示文字'
                        },
                        {
                            label: '累计已上架未出版教材',
                            value: this.pageData['累计已上架未出版教材'],
                            tip: '提示文字' 
                        },
                        {
                            label: '累计添加已出版教材',
                            value: this.pageData['累计添加已出版教材'],
                            tip: '提示文字' 
                        }
                    ]
                    // 申请额度
                    // 使用额度
                    let schoolList = this.pageData['学校额度使用列表'].map(v=>v.schoolName);
                    let bar1Data = this.pageData['学校额度使用列表'].map(item => item['分配额度']);
                    let bar2Data = this.pageData['学校额度使用列表'].map(item => item['使用额度']);
                    this.bar1Option.xAxis[0].data = schoolList;
                    this.bar1Option.series[0].data = bar1Data;
                    this.bar1Option.series[1].data = bar2Data;
                    // 教师&学生
                    let teacherList = this.pageData['学校师生数量列表'].map(v=>v.schoolName);
                    let bar3Data = this.pageData['学校师生数量列表'].map(item => item['教师数量']);
                    let bar4Data = this.pageData['学校师生数量列表'].map(item => item['学生数量']);
                    this.bar2Option.xAxis[0].data = teacherList;
                    this.bar2Option.series[0].data = bar3Data;
                    this.bar2Option.series[1].data = bar4Data;

                    this.textBookData = this.pageData['教材使用率列表'];
                    
                    // 为了匹配截图效果，使用我们设置的静态数据而不是API返回的数据
                    this.pieOption.series[0].data.forEach(v=>{
                        if(v.name=='中职'){
                             v.value = this.pageData['中职数量'];
                        }else if(v.name=='高职'){
                            v.value = this.pageData['高职数量'];
                        }else if(v.name=='本科'){
                            v.value = this.pageData['本科数量'];
                        }
                    });

                    this.initBar();
                    
                }
            })
        }
        
    }
}
</script>
<style lang="scss">
.plant-branch-page{
    background-color: #F6F8FA;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    .content{
       padding: 0 20px;
        .title{
            font-size: 16px;
            color: #333;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            i{
                color: #409EFF;
                font-size: 20px;
                margin-right: 10px;   
            }
            span{
             font-family: PingFangSC, PingFang SC;
             font-weight: 400;
             font-size: 14px;
             margin-left: 8px;
             color: #999999;
            }
        }
        .part-label{
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            position: relative;
            padding-left: 12px;
            margin: 0px;
            &::before{
                content: "";
                display: inline-block;
                position: absolute;
                width: 3px;
                height: 12px;
                background: #0070FC;
                border-radius: 2px;
                left: 0;
                top: 3px;
            }
        }
       .base-data{
            height: 328px;
            background: #FFFFFF;
            padding: 20px;
            .data-box{
                display: flex;
                justify-content: flex-start;
                margin-top: 10px;
                padding-bottom: 20px;
                border-bottom: 1px solid #F2F3F5;
            }
           .data-item{
                width: 216px;
                height: 100px;
                background: #FBFBFB;
                border-radius: 4px; 
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                padding-left: 20px;
                margin-right: 15px;
                .data-label{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    margin-bottom: 4px;
                }
                .data-value{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 26px;
                    color: #0070FC;
                }
           }

           .data-chart{
                width:243px;
                height:102px
           }
       }
       .data-static{
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        .textBook-data{
            width: 50%;
            height: 392px;
            padding: 20px;
            background: #FFFFFF;
        }
        .user-data{
            width: 49%;
            height: 392px;
            padding: 20px;
            background: #FFFFFF;
        }

        .login-data{
            width: calc(65% - 10px);
            height: 392px;
            padding: 20px;
            background: #FFFFFF;
            margin-right: 10px;
        }
        .book-data{
            width: 35%;
            height: 392px;
            padding: 20px;
            background: #FFFFFF;
        }
       }
    }
}
</style>