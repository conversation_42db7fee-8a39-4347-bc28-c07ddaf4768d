<template>
    <div class="permission-manage-page" v-loading="loading">
        <div class="left-nav">
          <div class="top-btn">
            <el-button @click="addGroup(1)" class="add-btn"><i class="iconfont icon-xinjian"></i> 新增分组</el-button>
            <el-button @click="addGroup(2)" class="add-btn"><i class="iconfont icon-xinjian"></i> 新增角色</el-button>
          </div>
          <div>
            <p class="all-tip" @click="handleNodeClick({id:0})">全部</p>
            <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick">
              <template slot-scope="{ data }">
                <span class="tree-node-content" :class="{ 'active': selectedNodeId === data.id }">
                <el-tooltip :content="data.name" :disabled="data.name.length<9"  placement="right">
                  <span class="tree-node-text" :title="data.name">{{ data.name }}</span>
                </el-tooltip>
                <span class="tree-node-actions" style="margin-left: 10px;">
                  <i class="iconfont icon-bianji" @click.stop="editGroup(data)" title="编辑"></i>
                  <i class="iconfont icon-shanchu" @click.stop="deleteGroup(data)" title="删除"></i>
                </span>
              </span>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="right-content">
            <div class="top-tool">
              <el-input class="search-input" v-model="tableConfig.searchParams.keyword" placeholder="请输入角色名称" clearable></el-input>
              <el-select class="search-select" v-model="tableConfig.searchParams.enable" placeholder="请选择状态">
                <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-button @click="handleSearchPage()" class="search-btn">查询</el-button>
              <!-- <addButton/> -->
              <reflashButton @reflashEvent="reflashEvent"/>
              <exportButton/>
              <importButton/>
            </div>
            <table2 
              @selectionChange="selectionChange" 
              :notShowSearch="notShowSearch" 
              ref="tablePreview" 
              @selection-change="handleSelectionChange" 
              :selectable="tableConfig.selectable" 
              :data="tableConfig.tableData" 
              :columns="tableConfig.columns" 
              :queryFormConfig="tableConfig.queryConfig" 
              :total="tableConfig.total"
              :pagination="tableConfig.pagination" 
              :paginationLayout="tableConfig.paginationLayout" 
              :firstLoad="firstLoad" 
              :height="height" 
              :searchParams="tableConfig.searchParams" 
              @handleSearch="handleSearch">
              <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
              <template #operate>
                <el-table-column v-if="tableConfig.operateBtns && tableConfig.operateBtns.length" label="操作" fixed="right" :width="200">
                  <template slot-scope="scope">
                    <el-button v-for="(item, index) in tableConfig.operateBtns" type="text"
                              v-show="isShowOperateBtn(scope, item)"
                              :class="item.class || ''"
                              @click="btnClick(scope.row, item)" :key="index"
                              :disabled="scope.row.dataLocked">
                      {{ item.name }}
                    </el-button>
                  </template>
                </el-table-column>
              </template>
            </table2>
        </div>

        <!-- 新增分组 -->
        <baseDialog :noFooter="true" :title="groupForm.id ? '编辑分组' : '新增分组'" width="500px" :visible.sync="addGroupDialogVisible">
          <el-form :model="groupForm" :rules="rules" ref="formRef" label-width="120px">
            <el-form-item label="分组名称" prop="name">
              <el-input type="text" v-model="groupForm.name" maxLength="12" show-word-limit placeholder="请输入分组名称" />
            </el-form-item>
          </el-form>
          <div style="text-align:right;margin-bottom:0;margin-top:40px;">
            <el-button type="default" @click="addGroupDialogVisible = false;">取消</el-button>
            <el-button type="primary" @click="submitForm()">确定</el-button>
          </div>
        </baseDialog>

        <!-- 新增角色 -->
         <baseDialog :noFooter="true" :title="isViewDetail ? '角色详情' : '新增/编辑角色'" width="500px" :visible.sync="roleDialogVisible">
          <el-form class="role-form" :model="roleForm" :rules="roleRules" ref="roleFormRef" label-width="120px">
            <el-form-item label="角色名称" prop="name">
              <el-input v-model="roleForm.name" placeholder="请输入角色名称" :disabled="isViewDetail" />
            </el-form-item>
            <el-form-item label="角色编码" prop="code">
              <el-input v-model="roleForm.code" placeholder="请输入角色编码" :disabled="isViewDetail" />
            </el-form-item>
            <el-form-item label="角色标识" prop="identity">
              <el-input v-model="roleForm.identity" placeholder="请输入角色标识" :disabled="isViewDetail" />
            </el-form-item>
            <el-form-item label="所属分组" prop="groupId">
              <el-select v-model="roleForm.groupId" placeholder="请选择所属分组" :disabled="isViewDetail">
                <el-option v-for="group in data" :key="group.id" :label="group.name" :value="group.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否启用" prop="enable">
              <el-switch v-model="roleForm.enable" active-text="启用" inactive-text="禁用" :disabled="isViewDetail" />
            </el-form-item>
          </el-form>
          <div style="text-align:right;margin-bottom:0;margin-top:40px;">
            <el-button type="default" @click="handleRoleDialogClose">取消</el-button>
            <el-button v-if="!isViewDetail" type="primary" @click="submitRoleForm()">确定</el-button>
          </div>
        </baseDialog>

        <!-- 权限设置抽屉 -->
        <el-drawer 
          title="角色权限设置" 
          :visible.sync="rolePermissionDrawerVisible" 
          size="70%"
          :before-close="() => { this.rolePermissionDrawerVisible = false; }"
        >
          <div class="permission-drawer-content">
            <!-- 角色信息 -->
            <div class="role-info-section">
              <h3>角色信息</h3>
              <div class="role-info-item">
                <span class="label">角色名称：</span>
                <span class="value">{{ currentRoleData?.name }}</span>
              </div>
              <div class="role-info-item">
                <span class="label">角色编码：</span>
                <span class="value">{{ currentRoleData?.code }}</span>
              </div>
              <div class="role-info-item">
                <span class="label">所属分组：</span>
                <span class="value">{{ currentRoleData?.groupName }}</span>
              </div>
            </div>

            <!-- 权限列表 -->
            <div class="permission-list-section">
              <h3>权限配置</h3>
              <div v-if="rolePermissions.length === 0" class="no-permission-data">
                暂无权限数据
              </div>
              <div v-else class="permission-content">
                <!-- 端口权限 -->
                <div v-for="(portal, portalIndex) in rolePermissions" :key="portalIndex" class="portal-section">
                  <div class="portal-header">
                    <el-checkbox 
                      :checked="isPortalAllChecked(portal)" 
                      @change="selectAllPermissions($event, portalIndex)"
                    ></el-checkbox>
                    <span class="portal-name">{{ portal.portalName }}</span>
                  </div>
                  
                  <!-- 页面权限 -->
                  <div class="pages-container">
                    <div v-for="(page, pageIndex) in portal.pages" :key="pageIndex" class="page-section">
                      <div class="page-header">
                        <el-checkbox 
                          :checked="isPageAllChecked(page)" 
                          @change="selectPagePermissions($event, portalIndex, pageIndex)"
                        ></el-checkbox>
                        <span class="page-name">{{ page.name }}</span>
                      </div>
                      
                      <!-- 操作权限 -->
                      <div class="actions-container">
                        <div v-for="(action, actionIndex) in page.actions" :key="actionIndex" class="action-item">
                          <el-checkbox 
                            v-model="action.checked" 
                            :label="action.name"
                          ></el-checkbox>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 底部按钮 -->
            <div class="drawer-footer">
              <el-button @click="rolePermissionDrawerVisible = false">取消</el-button>
              <el-button type="primary" @click="saveRolePermissions">保存</el-button>
            </div>
          </div>
        </el-drawer>
    </div>
</template>
  
<script>
  import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
import { getToken } from "@/utils/token";
  
  export default {
    name: "permissionManagePage",
    data() {
      return {
        loading:false,
        data:[],
        selectedNodeId: 0,
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        addGroupDialogVisible:false,
        isEdit: false,
        isEditRole: false,
        isViewDetail: false,
        groupForm:{
          name:'',
          parentId:0,
        },
        roleDialogVisible:false,
        roleForm:{
          groupId: "",
          code: "",  // 角色编码
          name: "",  // 角色名称
          identity: "", // 角色标识
          enable: true, // 是否启用
        },
        rules:{
          name:[
            { required: true, message: '请输入分组名称', trigger: 'blur' },
          ],
        },
        roleRules:{
          name:[
            { required: true, message: '请输入角色名称', trigger: 'blur' },
          ],
          code:[
            { required: true, message: '请输入角色编码', trigger: 'blur' },
          ],
          identity:[
            { required: true, message: '请输入角色标识', trigger: 'blur' },
          ],
          groupId:[
            { required: true, message: '请选择所属分组', trigger: 'change' },
          ],
        },
        notShowSearch: false,
        firstLoad: true,
        height: 560,
        enableList:[
          {
            value:true,
            label:'启用',
          },
          {
            value:false,
            label:'禁用',
          },
        ],
        rolePermissionDrawerVisible: false, // 权限设置抽屉可见性
        rolePermissions: [], // 角色权限数据
        currentRoleData: null, // 当前角色数据
        tableConfig:{
          tableData:[],
          columns:[
            {
              prop: 'id',
              label: 'ID',
            },
            {
              prop: 'name',
              label: '角色名称',
              width: '120'
            },
            {
              prop: 'code',
              label: '角色编码',
              width: '120'
            },
            {
              prop: 'identity',
              label: '角色标识',
              width: '120'
            },
            {
              prop: 'enable',
              label: '是否启用',
              width: '120',
              formatter:(row)=>{
                return row.enable?'启用':'禁用';
              }
            },
            {
              prop: 'groupName',
              label: '所属分组',
              width: '110',
            },
            {
              prop: 'createTime',
              label: '创建时间',
              formatter:(row)=>{
                return formatISOString(row.createTime);
              }
            },
          ],
          queryConfig:{
            keyword:'',
            enable:true,
            groupId:0,
          },
          total:0,
          pagination:true,
          paginationLayout:'total, sizes, prev, pager, next, jumper',
          selectable:true,
          operateBtns:[
            { name: '详情',class:'default-btn' },
            { name: '编辑',class:'default-btn' },
            { name: '角色权限',class:'default-btn' },
            { name: '删除',class:'del-btn' }
          ],
          searchParams:{
            pageIndex: 1,
            pageSize: 10,
            keyword:'',
            enable:true,
            groupId:0,
          },
          height:560,
        }
      }; 
    },
    components: {
      // 补全模板中使用的组件
      addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
      reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
      exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
      importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
      empty:()=>import("@/components/base/empty.vue"),
      baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    },
    mounted() {
      this.getGroupList();
      this.getRoleList();
    },
    methods: {
      // 获取分组列表
      getGroupList(){
        this.$api.RoleGetGroupList({}).then(res=>{
          if(res.errCode == 0){
            this.data = res.data;
          // 确保有节点被选中，默认选中第一个节点
          if (this.data.length > 0 && !this.selectedNodeId) {
            this.selectedNodeId = this.data[0].id;
          }
          }
        })
      },
      // 获取角色列表
      getRoleList(){
        this.$api.RoleGetRoleList(this.tableConfig.searchParams).then(res=>{
          if(res.errCode == 0){
            this.tableConfig.tableData = res.data.items;
            this.tableConfig.total = res.data.total;
          }
        })
      },
      handleNodeClick(data){
        this.selectedNodeId = data.id;
        this.tableConfig.searchParams.groupId = data.id;
        this.tableConfig.searchParams.pageIndex = 1;
        this.getRoleList()
      },
      handleSearch(params){
        this.tableConfig.searchParams = params;
        this.getRoleList();
      },
      reflashEvent(){
        this.handleSearchPage();
      },
      // 查询
      handleSearchPage(){
        this.tableConfig.searchParams.pageIndex = 1;
        this.getRoleList();
      },
      handleSelectionChange(val){
        console.log(val);
      },
      selectionChange(val){
        // 处理选择变化
        console.log('Selection changed:', val);
      },
      isShowOperateBtn(scope, item) {
        // 这里可以添加操作按钮显示逻辑
        return true;
      },
      btnClick(row, item) {
        console.log(row, item);
        // 按钮点击逻辑
        switch (item.name) {
          case '详情':
            this.showRoleDetail(row);
            break;
          case '编辑':
            this.editRole(row);
            break;
          case '删除':
            this.handleDelete(row)
            break; 
          case '角色权限':
            this.showRolePermission(row);
            break;  
        }
      },
      // 查看角色详情
      showRoleDetail(row) {
        this.isViewDetail = true;
        this.roleForm = {
          id: row.id,
          groupId: row.groupId,
          code: row.code,
          name: row.name,
          identity: row.identity,
          enable: row.enable
        };
        this.roleDialogVisible = true;
      },
      
      // 处理角色弹窗关闭
      handleRoleDialogClose() {
        this.roleDialogVisible = false;
        this.isViewDetail = false;
      },
      // 编辑角色
      editRole(row) {
        this.isEditRole = true;
        this.isViewDetail = false;
        this.roleForm = {
          id: row.id,
          groupId: row.groupId,
          code: row.code,
          name: row.name,
          identity: row.identity,
          enable: row.enable
        };
        this.roleDialogVisible = true;
      },
      async handleDelete(row){
          await this.$zdDialog({
            contImg: '',
            contTitle: '确定删除?',
            contDesc: '确定后，该角色会被删除, 是否继续?',
          })
          this.$api.RoleDeleteRole({id:row.id}).then(res=>{
            if(res.errCode == 0){
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.getRoleList();
              this.getGroupList();
            }
          })
      },
      // 新增分组或角色
      addGroup(type){
        if(type === 1){
          this.isEdit = false;
          this.groupForm = {
            name:'',
            parentId:0,
          };
          this.addGroupDialogVisible = true;
        } else if(type === 2){
          this.addRole();
        }
      },
      async showRolePermission(row){
        this.currentRoleData = row;
        try {
          this.loading = true;
          // 获取权限列表数据
          let { data, errCode } = await this.$api.RoleGetPermissions({});
          
          if (errCode === 0 && data && data.length > 0) {
            // 深拷贝数据，避免直接修改原数据
            this.rolePermissions = JSON.parse(JSON.stringify(data));
            // 初始化checked状态为false，使用$set确保响应式
            this.rolePermissions.forEach(portal => {
              portal.pages.forEach(page => {
                page.actions.forEach(action => {
                  this.$set(action, 'checked', false);
                });
              });
            });
            
            // 这里可以根据实际业务逻辑，从API获取当前角色已有的权限并设置checked状态为true
            // 例如: this.setRoleExistingPermissions(row.id);
          } else {
            this.rolePermissions = [];
          }
          
          // 显示抽屉
          this.rolePermissionDrawerVisible = true;
        } catch (error) {
          console.error('获取权限数据失败:', error);
          this.$message.error('获取权限数据失败，请重试');
        } finally {
          this.loading = false;
        }
      },
      
      // 检查整个端口的权限是否都被选中
      isPortalAllChecked(portal) {
        if (!portal || !portal.pages || portal.pages.length === 0) {
          return false;
        }
        
        for (let page of portal.pages) {
          if (!page.actions || page.actions.length === 0) {
            continue;
          }
          
          for (let action of page.actions) {
            if (!action.checked) {
              return false;
            }
          }
        }
        
        return true;
      },
      
      // 检查整个页面的权限是否都被选中
      isPageAllChecked(page) {
        if (!page || !page.actions || page.actions.length === 0) {
          return false;
        }
        
        for (let action of page.actions) {
          if (!action.checked) {
            return false;
          }
        }
        
        return true;
      },
      
      // 选择/取消选择整个端口的权限
      selectAllPermissions(checked, portalIndex) {
        if (!this.rolePermissions[portalIndex] || !this.rolePermissions[portalIndex].pages) {
          return;
        }
        
        this.rolePermissions[portalIndex].pages.forEach(page => {
          if (page.actions) {
            page.actions.forEach(action => {
              this.$set(action, 'checked', checked);
            });
          }
        });
      },
      
      // 选择/取消选择整个页面的权限
      selectPagePermissions(checked, portalIndex, pageIndex) {
        if (!this.rolePermissions[portalIndex] || 
            !this.rolePermissions[portalIndex].pages || 
            !this.rolePermissions[portalIndex].pages[pageIndex] || 
            !this.rolePermissions[portalIndex].pages[pageIndex].actions) {
          return;
        }
        
        this.rolePermissions[portalIndex].pages[pageIndex].actions.forEach(action => {
          this.$set(action, 'checked', checked);
        });
      },
      
      // 保存角色权限设置
      async saveRolePermissions() {
        try {
          this.loading = true;
          
          // 收集选中的权限，按照API要求的格式
          const permissionConfig = {
            pages: [],
            actions: []
          };
          
          this.rolePermissions.forEach(portal => {
            portal.pages.forEach(page => {
              // 检查页面是否有选中的操作权限
              const hasCheckedActions = page.actions.some(action => action.checked);
               
              if (hasCheckedActions) {
                // 将页面ID添加到pages数组，如果没有id属性，使用name或value作为备选
                const pageId = page.id || page.name || page.value;
                if (pageId) {
                  permissionConfig.pages.push(pageId);
                }
                 
                // 收集页面中所有选中的操作权限
                page.actions.forEach(action => {
                  if (action.checked) {
                    permissionConfig.actions.push(action.value);
                  }
                });
              }
            });
          });
          
          // 调用保存权限的API
          const result = await this.$api.RoleUpdateRolePermission({
            roleId: this.currentRoleData.id,
            permissionConfig: permissionConfig
          });
          
          if (result.errCode === 0) {
            this.$message.success('权限设置保存成功');
            this.rolePermissionDrawerVisible = false;
          } else {
            this.$message.error(result.errMsg || '权限设置保存失败');
          }
        } catch (error) {
          console.error('保存权限设置失败:', error);
          this.$message.error('保存权限设置失败，请重试');
        } finally {
          this.loading = false;
        }
      },
      // 编辑分组
      editGroup(data){
        this.isEdit = true;
        this.groupForm = {
          id: data.id,
          name: data.name,
          parentId: data.parentId || 0,
        };
        this.addGroupDialogVisible = true;
      },
      
      // 删除分组
      async deleteGroup(data){
        try {
          await this.$zdDialog({
            contImg: '',
            contTitle: '确定删除?',
            contDesc: '确定后，该分组及其下所有角色会被删除, 是否继续?',
          });
          
          // 调用删除分组的API
          this.$api.RoleDeleteGroup({id: data.id}).then(res => {
            if(res.errCode == 0){
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.selectedNodeId = 0;
              this.tableConfig.searchParams.groupId = 0;
              this.getGroupList();
              this.getRoleList();
            }
          });
        } catch (error) {
          // 用户取消删除
          console.log('取消删除');
        }
      },
      submitForm(){
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            if (this.isEdit) {
              // 编辑分组
              this.$api.RoleUpdateGroup(this.groupForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '编辑成功',
                    type: 'success'
                  });
                  this.addGroupDialogVisible = false;
                  this.$refs.formRef.resetFields();
                  this.getGroupList();
                }
              })
            } else {
              // 新增分组
              this.$api.RoleCreateGroup(this.groupForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  });
                  this.addGroupDialogVisible = false;
                  this.$refs.formRef.resetFields();
                  this.getGroupList();
                }
              })
            }
          } else {
            this.$message({
              message: '请填写完整信息',
              type: 'error'
            });
          }
        });
      },
      // 新增角色
      addRole(){
        // 重置角色表单
        this.isEditRole = false;
        this.roleForm = {
          groupId: this.selectedNodeId,
          code: "",
          name: "",
          identity: "",
          enable: true,
        };
        this.roleDialogVisible = true;
      },
      // 提交角色表单
      submitRoleForm(){        
        this.$refs.roleFormRef.validate((valid) => {
          if (valid) {
            if (this.isEditRole) {
              // 编辑角色
              this.$api.RoleUpdateRole(this.roleForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '角色编辑成功',
                    type: 'success'
                  });
                  this.roleDialogVisible = false;
                  this.isEditRole = false;
                  this.getGroupList();
                  this.getRoleList();
                  
                }
              });
            } else {
              // 新增角色
              this.$api.RoleCreateRole(this.roleForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '角色新增成功',
                    type: 'success'
                  });
                  this.roleDialogVisible = false;
                  // 刷新角色列表
                  this.getGroupList();
                  this.getRoleList();
                }
              });
            }
          } else {
            this.$message({
              message: '请填写完整信息',
              type: 'error'
            });
          }
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
    .permission-manage-page{
      display: flex;
      justify-content: space-between;
      height: 100%;
      padding: 20px;
      background: #fff;
      padding-bottom: 0;
      box-sizing: border-box;
      .left-nav{
        width:240px;
        height: 100%;
        background: #fff;
        box-sizing: border-box;
        padding:10px;
        border-right:1px solid #E7E7E7;
        overflow-y: auto;
        overflow-x: hidden;
        .top-btn{
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
        .add-btn{
          width: 105px;
          height: 38px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #E7E7E7;
          padding:0;
          color: #333333;
        }

         .all-tip{
          height: 38px;
          line-height:38px;
          background: #F1F7FF;
          border-radius: 4px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #026CFF;
          text-align: left;
          font-style: normal;
          padding-left:20px;
          margin-top:12px;
          margin-bottom:10px;
          cursor: pointer;
        }

        ::v-deep .el-tree-node__content{
          height: 38px;
          line-height:38px;
        }
        
        .tree-node-content{
          width: 100%;
          height:38px;
          padding-right:30px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          font-size:14px;
          color:#333;
        }
        
        .tree-node-text{
          display: inline-block;
          width: 100%;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          padding-left:16px;
        }
        
        .tree-node-content.active {
          background-color: #e6f4ff;
          color: #026CFF;
          border-radius: 4px;
          padding: 2px 5px;
        }
        
        .tree-node-content.active .tree-node-text {
          color: #026CFF;
          font-weight: 500;
        }
        
        .tree-node-actions{
          visibility: hidden;
        }
        ::v-deep .el-tree-node__expand-icon.is-leaf{
          display: none;
        }
        ::v-deep .el-tree-node__content:hover .tree-node-actions{
          visibility: visible;
        }
        
        .iconfont{
          margin: 0 5px;
          cursor: pointer;
          font-size: 14px;
        }
        
        .icon-bianji{
          color: #026CFF;
          font-size: 14px;
        }
        
        .icon-shanchu{
          color: #F56C6C;
          font-size: 18px;
        }
      }

      .right-content{
        flex: 1;
        width: calc(100% - 240px);
        height: 100%;
        background: #fff;
        box-sizing: border-box;
        padding: 20px;
        .top-tool{
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .search-input{
            width: 300px;
            margin-right:10px;
          }
          .search-select{
            width: 220px;
            margin-right:10px;
          }
          .search-btn{
            width: 80px;
            height: 38px;
            margin-right:10px;
            border-radius: 4px;
            border: 1px solid #07C392;
            &:hover{
              background: #07C392;
              color:#fff;
            }
         
          }
        }
       
      }
    }

    .role-form{
      ::v-deep .el-form-item{
        margin-bottom: 20px!important;
      }
    }
  </style>
  
  <style lang="scss" scoped>
    /* 权限设置抽屉样式 */
    .permission-drawer-content {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .role-info-section {
      padding: 20px;
      background-color: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      margin-bottom: 20px;
    }
    
    .role-info-section h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 600;
    }
    
    .role-info-item {
      margin-bottom: 10px;
    }
    
    .role-info-item .label {
      display: inline-block;
      width: 100px;
      color: #666;
    }
    
    .role-info-item .value {
      color: #333;
      font-weight: 500;
    }
    
    .permission-list-section {
      flex: 1;
      overflow-y: auto;
      padding: 0 20px;
    }
    
    .permission-list-section h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
    }
    
    .no-permission-data {
      text-align: center;
      color: #999;
      padding: 40px 0;
    }
    
    .portal-section {
      margin-bottom: 30px;
    }
    
    .portal-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 600;
    }
    
    .portal-header .el-checkbox {
      margin-right: 10px;
    }
    
    .pages-container {
      margin-left: 40px;
    }
    
    .page-section {
      margin-bottom: 20px;
    }
    
    .page-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: 500;
    }
    
    .page-header .el-checkbox {
      margin-right: 8px;
    }
    
    .actions-container {
      display: flex;
      flex-wrap: wrap;
      margin-left: 30px;
      margin-bottom: 10px;
    }
    
    .action-item {
      margin-right: 20px;
      margin-bottom: 8px;
    }
    
    .drawer-footer {
      padding: 20px;
      border-top: 1px solid #e8e8e8;
      display: flex;
      justify-content: flex-end;
    }
    
    .drawer-footer .el-button {
      margin-left: 10px;
    }
  </style>