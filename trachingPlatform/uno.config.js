import { defineConfig, presetAttributify, presetUno, transformerVariantGroup, transformerDirectives, presetIcons } from 'unocss'
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'

export default defineConfig({
  presets: [
    presetAttributify(),
    presetUno(),
    presetIcons({
      scale: 1,
      warn: true,
      extraProperties: {
        display: 'inline-block'
      },
      collections: {
        local: FileSystemIconLoader('./src/assets/svg')
      }
    })
  ],
  transformers: [transformerVariantGroup(), transformerDirectives()],
  shortcuts: {
    'w-full-flex-col': 'w-full flex flex-col',
    'w-full-flex': 'w-full flex',
    'h-full-flex': 'h-full flex',
    'wh-full': 'w-full h-full',
    'flex-1-hidden': 'flex-1 overflow-hidden',
    'primary-green-btn': 'bg-#07C392 b-none h-38px hover:bg-#10D3A0 active:bg-#10D3A0',
    'primary-blue-btn': '!bg-#3274FE !b-none h-38px !hover:bg-#0086FC !active:bg-#0086FC'
  },
  theme: {
    colors: {
      text_primary: '#3A3F50',
      text_secondary: '#393D4E',
      border_primary: '#e1e4eb'
    }
  }
})
